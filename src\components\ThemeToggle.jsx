import React from 'react';
import { useTheme } from '../contexts/ThemeContext';

const ThemeToggle = ({ showLabel = false, size = 'normal' }) => {
  const { toggleTheme, isDark } = useTheme();

  const sizeClasses = {
    small: 'p-1 text-lg',
    normal: 'p-2 text-2xl',
    large: 'p-3 text-3xl'
  };

  return (
    <button
      onClick={toggleTheme}
      className={`${sizeClasses[size]} rounded-xl bg-white bg-opacity-10 hover:bg-opacity-20 transition-all duration-300 transform hover:scale-105 flex items-center space-x-2 space-x-reverse`}
      title={isDark ? 'تبديل للوضع الفاتح' : 'تبديل للوضع الداكن'}
    >
      <span>{isDark ? '☀️' : '🌙'}</span>
      {showLabel && (
        <span className="text-white text-sm font-medium">
          {isDark ? 'فاتح' : 'داكن'}
        </span>
      )}
    </button>
  );
};

export default ThemeToggle;
