import React, { useState, useEffect } from 'react';
import firebaseService from './services/firebaseService';

const Inventory = () => {
  const [products, setProducts] = useState([]);
  const [form, setForm] = useState({ name: '', purchasePrice: '', salePrice: '', category: '', quantity: '' });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(false);

  const categories = {
    'هواتف': { icon: '📱', color: 'bg-blue-100 text-blue-800' },
    'إكسسوارات': { icon: '🎧', color: 'bg-green-100 text-green-800' },
    'حواسيب': { icon: '💻', color: 'bg-purple-100 text-purple-800' },
    'أجهزة منزلية': { icon: '🏠', color: 'bg-yellow-100 text-yellow-800' },
    'ملابس': { icon: '👕', color: 'bg-pink-100 text-pink-800' },
  };

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const data = await firebaseService.getProducts();
      setProducts(data);
      console.log('✅ تم تحميل المنتجات من Firebase');
    } catch (error) {
      console.error('Error loading products:', error);
      // Fallback to localStorage if Firebase fails
      const savedProducts = JSON.parse(localStorage.getItem('products') || '[]');
      setProducts(savedProducts);
      console.log('⚠️ تم تحميل المنتجات من التخزين المحلي');
    } finally {
      setLoading(false);
    }
  };

  const addProduct = async () => {
    if (!form.name || !form.purchasePrice || !form.salePrice || !form.category || !form.quantity) return;

    try {
      setLoading(true);
      const productData = {
        name: form.name,
        purchasePrice: parseFloat(form.purchasePrice),
        salePrice: parseFloat(form.salePrice),
        quantity: parseInt(form.quantity),
        category: form.category,
        profit: parseFloat(form.salePrice) - parseFloat(form.purchasePrice)
      };

      try {
        await firebaseService.createProduct(productData);
        await loadProducts();
        alert('✅ تم إضافة المنتج بنجاح في Firebase!');
      } catch (firebaseError) {
        // Fallback to localStorage
        const newProduct = {
          ...productData,
          id: Date.now()
        };
        const updatedProducts = [...products, newProduct];
        setProducts(updatedProducts);
        localStorage.setItem('products', JSON.stringify(updatedProducts));
        alert('✅ تم إضافة المنتج محلياً (سيتم المزامنة عند توفر الاتصال)');
      }

      setForm({ name: '', purchasePrice: '', salePrice: '', category: '', quantity: '' });

    } catch (error) {
      console.error('Error adding product:', error);
      alert('❌ حدث خطأ في إضافة المنتج: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const deleteProduct = async (id) => {
    if (!window.confirm('❗ هل تريد حذف هذا المنتج؟')) return;

    try {
      setLoading(true);

      // حذف من Firebase
      await firebaseService.deleteProduct(id);

      // تحديث الحالة المحلية
      const updatedProducts = products.filter(p => p.id !== id);
      setProducts(updatedProducts);

      // حفظ نسخة احتياطية محلية
      localStorage.setItem('products', JSON.stringify(updatedProducts));

      console.log('✅ تم حذف المنتج من Firebase بنجاح');

    } catch (error) {
      console.error('خطأ في حذف المنتج:', error);

      // في حالة فشل الحذف من Firebase، احذف محلياً فقط
      const updatedProducts = products.filter(p => p.id !== id);
      setProducts(updatedProducts);
      localStorage.setItem('products', JSON.stringify(updatedProducts));

      alert('⚠️ تم حذف المنتج محلياً فقط. تحقق من الاتصال بالإنترنت.');
    } finally {
      setLoading(false);
    }
  };

  const restockProduct = async (id) => {
    const quantity = prompt('كم قطعة تريد إضافتها للمخزون؟');
    if (!quantity || isNaN(quantity) || parseInt(quantity) <= 0) return;

    try {
      setLoading(true);

      // العثور على المنتج الحالي
      const currentProduct = products.find(p => p.id === id);
      if (!currentProduct) {
        alert('❌ المنتج غير موجود');
        return;
      }

      // تحديث الكمية
      const updatedProduct = {
        ...currentProduct,
        quantity: currentProduct.quantity + parseInt(quantity)
      };

      // تحديث في Firebase
      await firebaseService.updateProduct(id, updatedProduct);

      // تحديث الحالة المحلية
      const updatedProducts = products.map(product =>
        product.id === id ? updatedProduct : product
      );
      setProducts(updatedProducts);

      // حفظ نسخة احتياطية محلية
      localStorage.setItem('products', JSON.stringify(updatedProducts));

      alert(`✅ تم إضافة ${quantity} قطعة للمخزون`);
      console.log('✅ تم تحديث المخزون في Firebase بنجاح');

    } catch (error) {
      console.error('خطأ في تحديث المخزون:', error);

      // في حالة فشل التحديث في Firebase، حدث محلياً فقط
      const updatedProducts = products.map(product =>
        product.id === id
          ? { ...product, quantity: product.quantity + parseInt(quantity) }
          : product
      );
      setProducts(updatedProducts);
      localStorage.setItem('products', JSON.stringify(updatedProducts));

      alert(`⚠️ تم إضافة ${quantity} قطعة محلياً فقط. تحقق من الاتصال بالإنترنت.`);
    } finally {
      setLoading(false);
    }
  };

  const getFilteredProducts = () => {
    return products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  };

  const getTotalValue = () => {
    return products.reduce((sum, product) => sum + (product.salePrice * product.quantity), 0);
  };

  const getTotalProfit = () => {
    return products.reduce((sum, product) => sum + (product.profit * product.quantity), 0);
  };

  const getLowStockProducts = () => {
    return products.filter(product => product.quantity <= 5);
  };

  const getOutOfStockProducts = () => {
    return products.filter(product => product.quantity === 0);
  };

  return (
    <div className="p-6 max-w-7xl mx-auto min-h-screen">
      <div className="text-center mb-8 animate-fadeInUp">
        <h1 className="text-4xl font-bold text-gradient mb-2">📋 إدارة المخزون</h1>
        <p className="text-white text-opacity-80">إدارة المنتجات والكميات</p>
      </div>

      {/* تحذيرات المخزون */}
      {(getLowStockProducts().length > 0 || getOutOfStockProducts().length > 0) && (
        <div className="mb-8 space-y-4 animate-slideInRight">
          {getOutOfStockProducts().length > 0 && (
            <div className="bg-red-500 bg-opacity-20 border border-red-400 p-6 rounded-3xl shadow-glow-red">
              <h3 className="font-bold text-red-200 mb-4 text-xl flex items-center">
                <span className="text-2xl ml-3">⚠️</span>
                منتجات نفدت من المخزون ({getOutOfStockProducts().length})
              </h3>
              <div className="text-sm text-red-100 space-x-2 space-x-reverse">
                {getOutOfStockProducts().slice(0, 3).map(product => (
                  <span key={product.id} className="inline-block bg-red-400 bg-opacity-30 px-3 py-2 rounded-2xl mr-2 mb-2 backdrop-blur-sm">
                    {product.name}
                  </span>
                ))}
                {getOutOfStockProducts().length > 3 && <span className="text-red-200">وأكثر...</span>}
              </div>
            </div>
          )}

          {getLowStockProducts().length > 0 && (
            <div className="bg-yellow-500 bg-opacity-20 border border-yellow-400 p-6 rounded-3xl shadow-glow-yellow">
              <h3 className="font-bold text-yellow-200 mb-4 text-xl flex items-center">
                <span className="text-2xl ml-3">⚡</span>
                منتجات قليلة المخزون ({getLowStockProducts().length})
              </h3>
              <div className="text-sm text-yellow-100 space-x-2 space-x-reverse">
                {getLowStockProducts().slice(0, 3).map(product => (
                  <span key={product.id} className="inline-block bg-yellow-400 bg-opacity-30 px-3 py-2 rounded-2xl mr-2 mb-2 backdrop-blur-sm">
                    {product.name} ({product.quantity})
                  </span>
                ))}
                {getLowStockProducts().length > 3 && <span className="text-yellow-200">وأكثر...</span>}
              </div>
            </div>
          )}
        </div>
      )}

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="glass p-6 rounded-3xl text-center shadow-glow animate-fadeInUp card-hover">
          <div className="text-4xl mb-3">📦</div>
          <h3 className="text-lg font-bold text-white mb-2">إجمالي المنتجات</h3>
          <p className="text-3xl font-bold text-gradient">{products.length}</p>
        </div>

        <div className="glass p-6 rounded-3xl text-center shadow-glow-green animate-fadeInUp card-hover" style={{ animationDelay: '0.1s' }}>
          <div className="text-4xl mb-3">💰</div>
          <h3 className="text-lg font-bold text-white mb-2">قيمة المخزون</h3>
          <p className="text-3xl font-bold text-green-300">{getTotalValue().toFixed(2)} د.ع</p>
        </div>

        <div className="glass p-6 rounded-3xl text-center shadow-glow animate-fadeInUp card-hover" style={{ animationDelay: '0.2s' }}>
          <div className="text-4xl mb-3">📈</div>
          <h3 className="text-lg font-bold text-white mb-2">الربح المتوقع</h3>
          <p className="text-3xl font-bold text-purple-300">{getTotalProfit().toFixed(2)} د.ع</p>
        </div>

        <div className="glass p-6 rounded-3xl text-center shadow-glow-red animate-fadeInUp card-hover" style={{ animationDelay: '0.3s' }}>
          <div className="text-4xl mb-3">⚠️</div>
          <h3 className="text-lg font-bold text-white mb-2">تحذيرات</h3>
          <p className="text-3xl font-bold text-red-300">{getOutOfStockProducts().length + getLowStockProducts().length}</p>
          <p className="text-xs text-white text-opacity-60">منتج يحتاج انتباه</p>
        </div>
      </div>

      {/* نموذج إضافة منتج */}
      <div className="glass p-8 rounded-3xl shadow-glow mb-8 animate-slideInRight">
        <h2 className="text-2xl font-bold mb-6 text-white flex items-center">
          <span className="text-3xl ml-3">➕</span>
          إضافة منتج جديد
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <input
            placeholder="اسم المنتج"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.name}
            onChange={(e) => setForm({ ...form, name: e.target.value })}
          />
          <input
            placeholder="سعر الشراء"
            type="number"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.purchasePrice}
            onChange={(e) => setForm({ ...form, purchasePrice: e.target.value })}
          />
          <input
            placeholder="سعر البيع"
            type="number"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.salePrice}
            onChange={(e) => setForm({ ...form, salePrice: e.target.value })}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <input
            placeholder="الكمية"
            type="number"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.quantity}
            onChange={(e) => setForm({ ...form, quantity: e.target.value })}
          />
          <select
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.category}
            onChange={(e) => setForm({ ...form, category: e.target.value })}
          >
            <option value="" className="text-gray-800">اختر الفئة</option>
            {Object.keys(categories).map((c) => (<option key={c} className="text-gray-800">{c}</option>))}
          </select>
        </div>

        <button
          onClick={addProduct}
          className="btn-gradient px-8 py-4 rounded-2xl text-white font-bold text-lg transform hover:scale-105 transition-all duration-300 shadow-glow"
        >
          <span className="flex items-center justify-center">
            <span className="text-xl ml-2">➕</span>
            إضافة المنتج
          </span>
        </button>
      </div>

      {/* البحث والفلترة */}
      <div className="glass p-6 rounded-3xl shadow-glow mb-8 animate-fadeInUp">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <input
            placeholder="🔍 البحث عن منتج..."
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <select
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
          >
            <option value="all" className="text-gray-800">جميع الفئات</option>
            {Object.keys(categories).map((c) => (<option key={c} className="text-gray-800">{c}</option>))}
          </select>
        </div>
      </div>

      {/* قائمة المنتجات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {getFilteredProducts().length === 0 ? (
          <div className="col-span-full text-center py-16 text-white text-opacity-60">
            <div className="text-6xl mb-4">📭</div>
            <p className="text-xl mb-2">لا توجد منتجات</p>
            <p className="text-sm opacity-80">ابدأ بإضافة منتجات جديدة</p>
          </div>
        ) : (
          getFilteredProducts().map((p, index) => (
            <div key={p.id} className={`glass rounded-3xl p-6 shadow-glow transition-all duration-300 transform hover:scale-105 card-hover animate-fadeInUp ${
              p.quantity === 0 ? 'border-2 border-red-400 shadow-glow-red' :
              p.quantity <= 5 ? 'border-2 border-yellow-400 shadow-glow-yellow' : 'border-2 border-transparent'
            }`} style={{ animationDelay: `${index * 0.1}s` }}>
              <div className="flex justify-between items-start mb-4">
                <div className={`inline-block px-4 py-2 text-sm rounded-2xl backdrop-blur-sm ${
                  categories[p.category]?.color?.replace('text-', 'text-white ').replace('bg-', 'bg-') || 'bg-white bg-opacity-20 text-white'
                }`}>
                  <span className="text-lg">{categories[p.category]?.icon || '📦'}</span>
                  <span className="mr-2 font-medium">{p.category}</span>
                </div>

                {p.quantity === 0 && (
                  <span className="bg-red-500 text-white px-3 py-1 text-sm rounded-2xl font-bold animate-pulse-custom">نفد</span>
                )}
                {p.quantity > 0 && p.quantity <= 5 && (
                  <span className="bg-yellow-500 text-white px-3 py-1 text-sm rounded-2xl font-bold animate-pulse-custom">قليل</span>
                )}
              </div>

              <h2 className="font-bold text-xl mb-4 text-white">{p.name}</h2>

              <div className="space-y-3 text-sm mb-6">
                <div className="flex justify-between items-center text-white">
                  <span className="flex items-center">
                    <span className="text-lg ml-2">💵</span>
                    سعر الشراء:
                  </span>
                  <span className="font-bold">{p.purchasePrice} د.ع</span>
                </div>
                <div className="flex justify-between items-center text-white">
                  <span className="flex items-center">
                    <span className="text-lg ml-2">💰</span>
                    سعر البيع:
                  </span>
                  <span className="font-bold">{p.salePrice} د.ع</span>
                </div>
                <div className={`flex justify-between items-center font-medium ${
                  p.quantity === 0 ? 'text-red-300' :
                  p.quantity <= 5 ? 'text-yellow-300' : 'text-green-300'
                }`}>
                  <span className="flex items-center">
                    <span className="text-lg ml-2">📦</span>
                    الكمية:
                  </span>
                  <span className="font-bold">
                    {p.quantity}
                    {p.quantity === 0 && ' (نفد المخزون)'}
                    {p.quantity > 0 && p.quantity <= 5 && ' (قليل)'}
                  </span>
                </div>
                <div className={`flex justify-between items-center font-medium ${p.profit > 0 ? 'text-green-300' : 'text-red-300'}`}>
                  <span className="flex items-center">
                    <span className="text-lg ml-2">📈</span>
                    الربح:
                  </span>
                  <span className="font-bold">{p.profit.toFixed(2)} د.ع</span>
                </div>
                <div className="flex justify-between items-center font-bold text-blue-300 text-base">
                  <span className="flex items-center">
                    <span className="text-lg ml-2">💎</span>
                    القيمة الإجمالية:
                  </span>
                  <span className="text-gradient">{(p.salePrice * p.quantity).toFixed(2)} د.ع</span>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => restockProduct(p.id)}
                  disabled={loading}
                  className="bg-green-500 bg-opacity-80 hover:bg-opacity-100 text-white px-4 py-3 rounded-2xl text-sm font-medium transition-all duration-300 transform hover:scale-105 flex-1 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  <span className="flex items-center justify-center">
                    <span className="text-lg ml-1">{loading ? '⏳' : '📦'}</span>
                    {loading ? 'جاري التحديث...' : 'إعادة تعبئة'}
                  </span>
                </button>
                <button
                  onClick={() => deleteProduct(p.id)}
                  disabled={loading}
                  className="bg-red-500 bg-opacity-80 hover:bg-opacity-100 text-white px-4 py-3 rounded-2xl text-sm font-medium transition-all duration-300 transform hover:scale-105 flex-1 backdrop-blur-sm disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                >
                  <span className="flex items-center justify-center">
                    <span className="text-lg ml-1">{loading ? '⏳' : '🗑️'}</span>
                    {loading ? 'جاري الحذف...' : 'حذف'}
                  </span>
                </button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Inventory;
