import React, { useState, useEffect } from 'react';
import apiService from './services/api';
import firebaseService from './services/firebaseService';

const Debts = () => {
  const [debts, setDebts] = useState([]);
  const [form, setForm] = useState({ customerName: '', amount: '', date: '', notes: '', customerPhone: '', dueDate: '' });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  // تحديد ما إذا كنا نستخدم Firebase أم API
  const useFirebase = !process.env.VITE_API_URL || process.env.VITE_USE_FIREBASE === 'true';

  useEffect(() => {
    loadDebts();

    // مراقبة حالة الاتصال
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const loadDebts = async () => {
    try {
      setLoading(true);
      setError('');

      let debtsData;
      if (useFirebase) {
        debtsData = await firebaseService.getDebts();
      } else {
        debtsData = await apiService.getDebts();
      }

      setDebts(debtsData || []);
    } catch (error) {
      console.error('خطأ في تحميل الديون:', error);
      setError('فشل في تحميل الديون. ' + (isOnline ? 'تحقق من الاتصال بالخادم.' : 'لا يوجد اتصال بالإنترنت.'));

      // في حالة الفشل، استخدم البيانات المحلية إن وجدت
      const localDebts = localStorage.getItem('local_debts');
      if (localDebts) {
        setDebts(JSON.parse(localDebts));
      }
    } finally {
      setLoading(false);
    }
  };

  const addDebt = async () => {
    if (!form.customerName || !form.amount || !form.date) {
      setError('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    try {
      setError('');
      const debtData = {
        customerName: form.customerName.trim(),
        amount: parseFloat(form.amount),
        originalAmount: parseFloat(form.amount),
        debtDate: new Date(form.date),
        dueDate: form.dueDate ? new Date(form.dueDate) : null,
        notes: form.notes.trim(),
        customerPhone: form.customerPhone.trim(),
        status: 'pending'
      };

      let newDebt;
      if (useFirebase) {
        newDebt = await firebaseService.createDebt(debtData);
      } else {
        newDebt = await apiService.createDebt(debtData);
      }

      setDebts(prevDebts => [newDebt, ...prevDebts]);
      setForm({ customerName: '', amount: '', date: '', notes: '', customerPhone: '', dueDate: '' });

      // حفظ نسخة محلية
      const updatedDebts = [newDebt, ...debts];
      localStorage.setItem('local_debts', JSON.stringify(updatedDebts));

    } catch (error) {
      console.error('خطأ في إضافة الدين:', error);
      setError('فشل في إضافة الدين: ' + error.message);

      // في حالة فشل الحفظ على الخادم، احفظ محلياً
      if (!isOnline || error.message === 'OFFLINE') {
        const localDebt = {
          id: 'local_' + Date.now(),
          ...debtData,
          createdAt: new Date(),
          isLocal: true
        };

        const updatedDebts = [localDebt, ...debts];
        setDebts(updatedDebts);
        localStorage.setItem('local_debts', JSON.stringify(updatedDebts));
        setForm({ customerName: '', amount: '', date: '', notes: '', customerPhone: '', dueDate: '' });
        setError('تم حفظ الدين محلياً. سيتم مزامنته عند عودة الاتصال.');
      }
    }
  };

  const deleteDebt = async (id) => {
    if (!window.confirm('❗ هل تريد حذف هذا الدين؟')) return;

    try {
      setError('');

      if (!id.toString().startsWith('local_')) {
        if (useFirebase) {
          await firebaseService.deleteDebt(id);
        } else {
          await apiService.deleteDebt(id);
        }
      }

      const updatedDebts = debts.filter((d) => d.id !== id);
      setDebts(updatedDebts);
      localStorage.setItem('local_debts', JSON.stringify(updatedDebts));

    } catch (error) {
      console.error('خطأ في حذف الدين:', error);
      setError('فشل في حذف الدين: ' + error.message);
    }
  };

  const totalDebts = debts.reduce((sum, debt) => sum + (debt.amount || 0), 0);
  const pendingDebts = debts.filter(debt => debt.status === 'pending' || !debt.status);
  const overdueDebts = debts.filter(debt => {
    if (!debt.dueDate) return false;
    return new Date(debt.dueDate) < new Date() && debt.status !== 'paid';
  });

  if (loading) {
    return (
      <div className="p-6 max-w-5xl mx-auto min-h-screen flex items-center justify-center">
        <div className="glass p-12 rounded-3xl text-center">
          <div className="text-6xl mb-4 animate-spin">⏳</div>
          <p className="text-white text-xl">جاري تحميل الديون...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-5xl mx-auto min-h-screen">
      <div className="text-center mb-8 animate-fadeInUp">
        <h1 className="text-4xl font-bold text-gradient mb-2">💳 إدارة الديون</h1>
        <p className="text-white text-opacity-80">متابعة ديون العملاء</p>
        {!isOnline && (
          <div className="mt-2 px-4 py-2 bg-yellow-500 bg-opacity-20 rounded-lg text-yellow-300">
            ⚠️ وضع عدم الاتصال - البيانات محفوظة محلياً
          </div>
        )}
      </div>

      {error && (
        <div className="glass p-4 rounded-2xl mb-6 bg-red-500 bg-opacity-20 border border-red-500 border-opacity-30">
          <p className="text-red-300 text-center">❌ {error}</p>
          <button
            onClick={() => setError('')}
            className="mt-2 text-red-200 hover:text-white transition-colors duration-200 block mx-auto"
          >
            إغلاق
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="glass p-6 rounded-3xl shadow-glow-yellow text-center animate-pulse-custom">
          <div className="text-4xl mb-2">📊</div>
          <h3 className="font-bold text-lg text-white mb-1">إجمالي الديون</h3>
          <p className="text-2xl font-bold text-gradient">{totalDebts.toFixed(2)} دينار</p>
          <p className="text-white text-opacity-70 text-sm">{debts.length} عميل</p>
        </div>

        <div className="glass p-6 rounded-3xl shadow-glow-blue text-center">
          <div className="text-4xl mb-2">⏰</div>
          <h3 className="font-bold text-lg text-white mb-1">ديون معلقة</h3>
          <p className="text-2xl font-bold text-blue-300">{pendingDebts.length}</p>
          <p className="text-white text-opacity-70 text-sm">دين معلق</p>
        </div>

        <div className="glass p-6 rounded-3xl shadow-glow-red text-center">
          <div className="text-4xl mb-2">🚨</div>
          <h3 className="font-bold text-lg text-white mb-1">ديون متأخرة</h3>
          <p className="text-2xl font-bold text-red-300">{overdueDebts.length}</p>
          <p className="text-white text-opacity-70 text-sm">دين متأخر</p>
        </div>
      </div>

      <div className="glass p-8 rounded-3xl shadow-glow mb-8 animate-slideInRight">
        <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
          <span className="text-3xl ml-3">➕</span>
          إضافة دين جديد
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <input
            placeholder="اسم العميل *"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.customerName}
            onChange={(e) => setForm({ ...form, customerName: e.target.value })}
            required
          />
          <input
            placeholder="المبلغ *"
            type="number"
            step="0.01"
            min="0"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.amount}
            onChange={(e) => setForm({ ...form, amount: e.target.value })}
            required
          />
          <input
            type="date"
            title="تاريخ الدين"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.date}
            onChange={(e) => setForm({ ...form, date: e.target.value })}
            required
          />
          <input
            placeholder="رقم الهاتف"
            type="tel"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.customerPhone}
            onChange={(e) => setForm({ ...form, customerPhone: e.target.value })}
          />
          <input
            type="date"
            title="تاريخ الاستحقاق"
            placeholder="تاريخ الاستحقاق"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.dueDate}
            onChange={(e) => setForm({ ...form, dueDate: e.target.value })}
          />
          <textarea
            placeholder="ملاحظات"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm resize-none"
            rows="1"
            value={form.notes}
            onChange={(e) => setForm({ ...form, notes: e.target.value })}
          />
        </div>

        <button
          onClick={addDebt}
          disabled={!form.customerName || !form.amount || !form.date}
          className="btn-gradient px-8 py-4 rounded-2xl text-white font-bold text-lg transform hover:scale-105 transition-all duration-300 shadow-glow disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          <span className="flex items-center justify-center">
            <span className="text-xl ml-2">➕</span>
            إضافة دين
          </span>
        </button>
      </div>

      <div className="space-y-6">
        {debts.length === 0 ? (
          <div className="glass p-12 rounded-3xl text-center text-white text-opacity-60">
            <div className="text-6xl mb-4">💳</div>
            <p className="text-xl mb-2">لا توجد ديون مسجلة</p>
            <p className="text-sm opacity-80">ابدأ بإضافة ديون العملاء</p>
          </div>
        ) : (
          debts.map((debt, index) => {
            const isOverdue = debt.dueDate && new Date(debt.dueDate) < new Date() && debt.status !== 'paid';
            const debtDate = debt.debtDate ? new Date(debt.debtDate).toLocaleDateString('ar-SA') : debt.date;
            const dueDate = debt.dueDate ? new Date(debt.dueDate).toLocaleDateString('ar-SA') : null;

            return (
              <div
                key={debt.id}
                className={`glass p-6 rounded-3xl shadow-glow card-hover animate-fadeInUp ${isOverdue ? 'border-2 border-red-500 border-opacity-50' : ''}`}
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex justify-between items-start">
                  <div className="text-white flex-1">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-bold text-xl flex items-center">
                        <span className="text-2xl ml-2">👤</span>
                        {debt.customerName}
                        {debt.isLocal && (
                          <span className="mr-2 px-2 py-1 bg-yellow-500 bg-opacity-30 text-yellow-300 text-xs rounded-full">
                            محلي
                          </span>
                        )}
                      </h3>

                      <div className="flex items-center space-x-2 space-x-reverse">
                        {isOverdue && (
                          <span className="px-3 py-1 bg-red-500 bg-opacity-30 text-red-300 text-sm rounded-full">
                            🚨 متأخر
                          </span>
                        )}
                        <span className={`px-3 py-1 text-sm rounded-full ${
                          debt.status === 'paid' ? 'bg-green-500 bg-opacity-30 text-green-300' :
                          debt.status === 'partial' ? 'bg-yellow-500 bg-opacity-30 text-yellow-300' :
                          'bg-blue-500 bg-opacity-30 text-blue-300'
                        }`}>
                          {debt.status === 'paid' ? '✅ مدفوع' :
                           debt.status === 'partial' ? '⏳ جزئي' :
                           '⏰ معلق'}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-white text-opacity-80">
                      <div className="space-y-2">
                        <p className="flex items-center text-lg">
                          <span className="text-xl ml-2">💰</span>
                          المبلغ: <span className="font-bold text-red-300 mr-2">{(debt.amount || 0).toFixed(2)} دينار</span>
                        </p>
                        <p className="flex items-center">
                          <span className="text-lg ml-2">📅</span>
                          تاريخ الدين: {debtDate}
                        </p>
                        {dueDate && (
                          <p className="flex items-center">
                            <span className="text-lg ml-2">⏰</span>
                            تاريخ الاستحقاق: <span className={isOverdue ? 'text-red-300 font-bold' : ''}>{dueDate}</span>
                          </p>
                        )}
                      </div>

                      <div className="space-y-2">
                        {debt.customerPhone && (
                          <p className="flex items-center">
                            <span className="text-lg ml-2">📞</span>
                            الهاتف: {debt.customerPhone}
                          </p>
                        )}
                        {debt.notes && (
                          <p className="flex items-start">
                            <span className="text-lg ml-2 mt-1">📝</span>
                            <span>ملاحظات: {debt.notes}</span>
                          </p>
                        )}
                        {debt.payments && debt.payments.length > 0 && (
                          <p className="flex items-center">
                            <span className="text-lg ml-2">💳</span>
                            الدفعات: {debt.payments.length}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col space-y-2 mr-4">
                    {debt.status !== 'paid' && (
                      <button
                        onClick={() => {/* TODO: إضافة دفعة */}}
                        className="bg-green-500 bg-opacity-80 hover:bg-opacity-100 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 text-sm"
                      >
                        <span className="flex items-center">
                          <span className="text-sm ml-1">💳</span>
                          دفعة
                        </span>
                      </button>
                    )}
                    <button
                      onClick={() => deleteDebt(debt.id)}
                      className="bg-red-500 bg-opacity-80 hover:bg-opacity-100 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 text-sm"
                    >
                      <span className="flex items-center">
                        <span className="text-sm ml-1">🗑️</span>
                        حذف
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* زر إعادة التحميل */}
      <div className="text-center mt-8">
        <button
          onClick={loadDebts}
          className="glass px-6 py-3 rounded-2xl text-white font-medium transition-all duration-300 transform hover:scale-105"
        >
          <span className="flex items-center justify-center">
            <span className="text-lg ml-2">🔄</span>
            إعادة تحميل الديون
          </span>
        </button>
      </div>
    </div>
  );
};

export default Debts;
