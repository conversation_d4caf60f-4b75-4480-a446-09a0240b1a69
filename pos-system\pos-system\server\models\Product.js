import mongoose from 'mongoose';

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  purchasePrice: {
    type: Number,
    required: true,
    min: 0
  },
  salePrice: {
    type: Number,
    required: true,
    min: 0
  },
  quantity: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  category: {
    type: String,
    required: true,
    enum: ['هواتف', 'إكسسوارات', 'حواسيب', 'أجهزة منزلية', 'ملابس']
  },
  profit: {
    type: Number,
    default: function() {
      return this.salePrice - this.purchasePrice;
    }
  },
  lowStockAlert: {
    type: Number,
    default: 5
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Calculate profit before saving
productSchema.pre('save', function(next) {
  this.profit = this.salePrice - this.purchasePrice;
  next();
});

// Virtual for total value
productSchema.virtual('totalValue').get(function() {
  return this.salePrice * this.quantity;
});

// Virtual for stock status
productSchema.virtual('stockStatus').get(function() {
  if (this.quantity === 0) return 'out_of_stock';
  if (this.quantity <= this.lowStockAlert) return 'low_stock';
  return 'in_stock';
});

productSchema.set('toJSON', { virtuals: true });

export default mongoose.model('Product', productSchema);
