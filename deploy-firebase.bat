@echo off
echo ========================================
echo        رفع التطبيق إلى Firebase
echo ========================================

echo جاري بناء التطبيق...
call npm run build

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء التطبيق
    pause
    exit /b 1
)

echo ✅ تم بناء التطبيق بنجاح

echo.
echo جاري رفع التطبيق إلى Firebase...
echo.

:: تحقق من وجود Firebase CLI
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI غير مثبت
    echo يرجى تثبيته أولاً: npm install -g firebase-tools
    pause
    exit /b 1
)

:: تسجيل الدخول إلى Firebase
echo تسجيل الدخول إلى Firebase...
firebase login

:: رفع التطبيق
echo رفع التطبيق...
firebase deploy --only hosting

if %errorlevel% eq 0 (
    echo.
    echo ========================================
    echo ✅ تم رفع التطبيق بنجاح!
    echo 🌐 الرابط: https://pos-system-bfc74.web.app
    echo ========================================
) else (
    echo ❌ فشل في رفع التطبيق
)

pause
