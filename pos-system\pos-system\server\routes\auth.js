import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import Settings from '../models/Settings.js';

const router = express.Router();

// Login
router.post('/login', async (req, res) => {
  try {
    const { pin } = req.body;

    if (!pin) {
      return res.status(400).json({ message: 'رمز الدخول مطلوب' });
    }

    const settings = await Settings.getSettings();
    
    if (pin !== settings.pin) {
      return res.status(401).json({ message: 'رمز الدخول غير صحيح' });
    }

    const token = jwt.sign(
      { authenticated: true },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    res.json({
      message: 'تم تسجيل الدخول بنجاح',
      token,
      settings: {
        theme: settings.theme,
        fontSize: settings.fontSize,
        businessName: settings.businessName
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'خطأ في الخادم' });
  }
});

// Verify token
router.get('/verify', async (req, res) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ message: 'لا يوجد رمز مصادقة' });
    }

    jwt.verify(token, process.env.JWT_SECRET);
    res.json({ message: 'الرمز صحيح' });
  } catch (error) {
    res.status(401).json({ message: 'رمز المصادقة غير صحيح' });
  }
});

export default router;
