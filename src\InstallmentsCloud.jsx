import React, { useState, useEffect } from 'react';
import firebaseService from './services/firebaseService';

const InstallmentsCloud = () => {
  // كلمة المرور الافتراضية لحماية الحذف
  const ADMIN_PASSWORD = "admin123";
  
  const [installments, setInstallments] = useState([]);
  const [form, setForm] = useState({
    customerName: '',
    customerPhone: '',
    totalAmount: '',
    interestRate: '',
    monthsCount: ''
  });
  const [editingId, setEditingId] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showInstallmentsModal, setShowInstallmentsModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [customerToDelete, setCustomerToDelete] = useState(null);
  const [password, setPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [cloudStatus, setCloudStatus] = useState('disconnected'); // connected, disconnected, syncing

  // تحميل البيانات من Firebase عند بدء التطبيق
  useEffect(() => {
    loadInstallments();
  }, []);

  const loadInstallments = async () => {
    try {
      setLoading(true);
      setCloudStatus('syncing');
      setError('');
      
      console.log('🔄 تحميل الأقساط من Firebase...');
      const cloudInstallments = await firebaseService.getInstallments();
      
      // تحديث البيانات المحلية مع البيانات السحابية
      const updatedInstallments = cloudInstallments.map(installment => {
        if (!installment.installmentsList) {
          // إنشاء قائمة أقساط للبيانات القديمة
          const installmentsList = Array.from({ length: installment.monthsCount || 12 }, (_, index) => ({
            id: index + 1,
            month: index + 1,
            amount: installment.monthlyPayment || 0,
            dueDate: new Date(Date.now() + (index + 1) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            isPaid: index < (installment.paidInstallments || 0),
            paidDate: index < (installment.paidInstallments || 0) ? new Date().toISOString().split('T')[0] : null
          }));
          
          return {
            ...installment,
            installmentsList
          };
        }
        return installment;
      });
      
      setInstallments(updatedInstallments);
      setCloudStatus('connected');
      
      // حفظ نسخة احتياطية محلية
      localStorage.setItem('cloud_installments_backup', JSON.stringify(updatedInstallments));
      
      console.log('✅ تم تحميل', updatedInstallments.length, 'قسط من Firebase');
    } catch (error) {
      console.error('❌ خطأ في تحميل الأقساط من Firebase:', error);
      setError('فشل في الاتصال بالخادم السحابي: ' + error.message);
      setCloudStatus('disconnected');
      
      // تحميل النسخة الاحتياطية المحلية
      const localBackup = localStorage.getItem('cloud_installments_backup');
      if (localBackup) {
        try {
          const parsedBackup = JSON.parse(localBackup);
          setInstallments(parsedBackup);
          console.log('📱 تم تحميل النسخة الاحتياطية المحلية');
        } catch (parseError) {
          console.error('❌ خطأ في تحليل النسخة الاحتياطية:', parseError);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  const calculateInstallmentDetails = () => {
    const principal = parseFloat(form.totalAmount) || 0;
    const monthlyInterestRate = parseFloat(form.interestRate) || 0;
    const months = parseInt(form.monthsCount) || 1;
    
    if (principal <= 0 || months <= 0) {
      return {
        principal: '0.00',
        interestAmount: '0.00',
        totalAmountWithInterest: '0.00',
        monthlyPayment: '0.00'
      };
    }
    
    // حساب الفائدة كنسبة مئوية ثم الجمع
    // الفائدة = المبلغ الأساسي × نسبة الفائدة ÷ 100
    const interestAmount = (principal * monthlyInterestRate) / 100;
    
    // المبلغ الإجمالي = المبلغ الأساسي + الفائدة
    const totalAmountWithInterest = principal + interestAmount;
    
    // القسط الشهري = المبلغ الإجمالي ÷ عدد الأشهر
    const monthlyPayment = totalAmountWithInterest / months;
    
    return {
      principal: principal.toFixed(2),
      interestAmount: interestAmount.toFixed(2),
      totalAmountWithInterest: totalAmountWithInterest.toFixed(2),
      monthlyPayment: monthlyPayment.toFixed(2)
    };
  };

  const addOrUpdateInstallment = async () => {
    console.log('➕ محاولة إضافة/تحديث قسط...');
    
    if (!form.customerName || !form.totalAmount || !form.interestRate || !form.monthsCount || !form.customerPhone) {
      setError('يرجى ملء جميع الحقول المطلوبة');
      console.log('❌ حقول مفقودة');
      return;
    }

    try {
      setError('');
      setCloudStatus('syncing');
      const { principal, interestAmount, totalAmountWithInterest, monthlyPayment } = calculateInstallmentDetails();
      
      const installmentData = {
        customerName: form.customerName.trim(),
        customerPhone: form.customerPhone.trim(),
        totalAmount: parseFloat(form.totalAmount),
        interestRate: parseFloat(form.interestRate),
        monthsCount: parseInt(form.monthsCount),
        interestAmount: parseFloat(interestAmount),
        totalAmountWithInterest: parseFloat(totalAmountWithInterest),
        monthlyPayment: parseFloat(monthlyPayment),
        paidInstallments: 0,
        installmentsList: Array.from({ length: parseInt(form.monthsCount) }, (_, index) => ({
          id: index + 1,
          month: index + 1,
          amount: parseFloat(monthlyPayment),
          dueDate: new Date(Date.now() + (index + 1) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          isPaid: false,
          paidDate: null
        })),
        status: 'active'
      };

      let updatedInstallment;
      
      if (editingId) {
        // تحديث قسط موجود
        updatedInstallment = await firebaseService.updateInstallment(editingId, installmentData);
        const updatedInstallments = installments.map(installment =>
          installment.id === editingId 
            ? { ...installment, ...installmentData }
            : installment
        );
        setInstallments(updatedInstallments);
        console.log('✏️ تم تحديث القسط في Firebase');
      } else {
        // إضافة قسط جديد
        updatedInstallment = await firebaseService.createInstallment(installmentData);
        setInstallments(prevInstallments => [updatedInstallment, ...prevInstallments]);
        console.log('➕ تم إضافة قسط جديد في Firebase');
      }

      setCloudStatus('connected');
      
      // حفظ نسخة احتياطية محلية
      const currentInstallments = editingId 
        ? installments.map(i => i.id === editingId ? updatedInstallment : i)
        : [updatedInstallment, ...installments];
      localStorage.setItem('cloud_installments_backup', JSON.stringify(currentInstallments));
      
      // إعادة تعيين النموذج
      resetForm();
      setError('');
      console.log('✅ تم حفظ القسط بنجاح في السحابة');
      
    } catch (error) {
      console.error('❌ خطأ في حفظ القسط:', error);
      setError('فشل في حفظ القسط في السحابة: ' + error.message);
      setCloudStatus('disconnected');
    }
  };

  const resetForm = () => {
    setForm({
      customerName: '',
      customerPhone: '',
      totalAmount: '',
      interestRate: '',
      monthsCount: ''
    });
    setEditingId(null);
    setShowAddForm(false);
  };

  const editInstallment = (installment) => {
    console.log('✏️ تحرير القسط:', installment.id);
    
    setForm({
      customerName: installment.customerName,
      customerPhone: installment.customerPhone,
      totalAmount: installment.totalAmount.toString(),
      interestRate: installment.interestRate.toString(),
      monthsCount: installment.monthsCount.toString()
    });
    setEditingId(installment.id);
    setShowAddForm(true);
  };

  const deleteInstallment = (id) => {
    console.log('🗑️ محاولة حذف القسط:', id);
    
    const installment = installments.find(i => i.id === id);
    if (!installment) return;
    
    // فتح نافذة كلمة المرور بدلاً من التأكيد المباشر
    setCustomerToDelete({ id, name: installment.customerName });
    setShowPasswordModal(true);
    setPassword('');
    setPasswordError('');
  };

  const confirmDelete = async () => {
    if (password !== ADMIN_PASSWORD) {
      setPasswordError('كلمة المرور غير صحيحة');
      return;
    }

    if (!customerToDelete) return;

    try {
      setCloudStatus('syncing');
      
      // حذف من Firebase
      await firebaseService.deleteInstallment(customerToDelete.id);
      
      const updatedInstallments = installments.filter(i => i.id !== customerToDelete.id);
      setInstallments(updatedInstallments);
      
      // حفظ نسخة احتياطية محلية
      localStorage.setItem('cloud_installments_backup', JSON.stringify(updatedInstallments));
      
      setCloudStatus('connected');
      console.log('✅ تم حذف القسط من Firebase');
      
      // إذا كان القسط المحذوف هو المحرر حالياً، إعادة تعيين النموذج
      if (editingId === customerToDelete.id) {
        resetForm();
      }

      // إغلاق النافذة وإعادة تعيين البيانات
      setShowPasswordModal(false);
      setCustomerToDelete(null);
      setPassword('');
      setPasswordError('');
      
    } catch (error) {
      console.error('❌ خطأ في حذف القسط:', error);
      setError('فشل في حذف القسط من السحابة: ' + error.message);
      setCloudStatus('disconnected');
    }
  };

  const cancelDelete = () => {
    setShowPasswordModal(false);
    setCustomerToDelete(null);
    setPassword('');
    setPasswordError('');
  };

  const payInstallment = async (customerId, installmentId) => {
    try {
      setCloudStatus('syncing');
      
      const updatedInstallments = installments.map(customer => {
        if (customer.id === customerId) {
          const updatedInstallmentsList = customer.installmentsList.map(inst => 
            inst.id === installmentId 
              ? { ...inst, isPaid: true, paidDate: new Date().toISOString().split('T')[0] }
              : inst
          );
          
          const paidCount = updatedInstallmentsList.filter(inst => inst.isPaid).length;
          
          return {
            ...customer,
            installmentsList: updatedInstallmentsList,
            paidInstallments: paidCount,
            status: paidCount >= customer.monthsCount ? 'completed' : 'active'
          };
        }
        return customer;
      });

      // تحديث في Firebase
      const customerToUpdate = updatedInstallments.find(c => c.id === customerId);
      if (customerToUpdate) {
        await firebaseService.updateInstallment(customerId, customerToUpdate);
      }

      setInstallments(updatedInstallments);
      
      // حفظ نسخة احتياطية محلية
      localStorage.setItem('cloud_installments_backup', JSON.stringify(updatedInstallments));
      
      // تحديث العميل المحدد في النافذة المنبثقة
      if (selectedCustomer && selectedCustomer.id === customerId) {
        const updatedCustomer = updatedInstallments.find(c => c.id === customerId);
        setSelectedCustomer(updatedCustomer);
      }
      
      setCloudStatus('connected');
      console.log('✅ تم دفع قسط وحفظه في Firebase');
      
    } catch (error) {
      console.error('❌ خطأ في دفع القسط:', error);
      setError('فشل في حفظ دفع القسط في السحابة: ' + error.message);
      setCloudStatus('disconnected');
    }
  };

  const cancelPayment = async (customerId, installmentId) => {
    try {
      setCloudStatus('syncing');
      
      const updatedInstallments = installments.map(customer => {
        if (customer.id === customerId) {
          const updatedInstallmentsList = customer.installmentsList.map(inst => 
            inst.id === installmentId 
              ? { ...inst, isPaid: false, paidDate: null }
              : inst
          );
          
          const paidCount = updatedInstallmentsList.filter(inst => inst.isPaid).length;
          
          return {
            ...customer,
            installmentsList: updatedInstallmentsList,
            paidInstallments: paidCount,
            status: paidCount >= customer.monthsCount ? 'completed' : 'active'
          };
        }
        return customer;
      });

      // تحديث في Firebase
      const customerToUpdate = updatedInstallments.find(c => c.id === customerId);
      if (customerToUpdate) {
        await firebaseService.updateInstallment(customerId, customerToUpdate);
      }

      setInstallments(updatedInstallments);
      
      // حفظ نسخة احتياطية محلية
      localStorage.setItem('cloud_installments_backup', JSON.stringify(updatedInstallments));
      
      // تحديث العميل المحدد في النافذة المنبثقة
      if (selectedCustomer && selectedCustomer.id === customerId) {
        const updatedCustomer = updatedInstallments.find(c => c.id === customerId);
        setSelectedCustomer(updatedCustomer);
      }
      
      setCloudStatus('connected');
      console.log('✅ تم إلغاء دفع القسط وحفظه في Firebase');
      
    } catch (error) {
      console.error('❌ خطأ في إلغاء دفع القسط:', error);
      setError('فشل في حفظ إلغاء دفع القسط في السحابة: ' + error.message);
      setCloudStatus('disconnected');
    }
  };

  const openInstallmentsModal = (customer) => {
    setSelectedCustomer(customer);
    setShowInstallmentsModal(true);
  };

  const closeInstallmentsModal = () => {
    setShowInstallmentsModal(false);
    setSelectedCustomer(null);
  };

  const syncWithCloud = async () => {
    await loadInstallments();
  };

  const totalInstallments = installments.length;
  const activeInstallments = installments.filter(i => i.status === 'active').length;
  const completedInstallments = installments.filter(i => i.status === 'completed').length;
  const totalRevenue = installments.reduce((sum, i) => sum + i.totalAmountWithInterest, 0);
  const totalInterestEarned = installments.reduce((sum, i) => sum + (i.interestAmount || 0), 0);

  return (
    <div className="p-6 max-w-7xl mx-auto min-h-screen">
      <div className="text-center mb-8 animate-fadeInUp">
        <h1 className="text-4xl font-bold text-gradient mb-2">☁️ منصة الأقساط السحابية</h1>
        <p className="text-white text-opacity-80">إدارة أقساط العملاء مع الحفظ السحابي</p>
        
        {/* مؤشر حالة السحابة */}
        <div className="mt-4 flex justify-center">
          <div className={`px-4 py-2 rounded-full text-sm font-medium flex items-center ${
            cloudStatus === 'connected' ? 'bg-green-500 bg-opacity-20 text-green-300' :
            cloudStatus === 'syncing' ? 'bg-yellow-500 bg-opacity-20 text-yellow-300' :
            'bg-red-500 bg-opacity-20 text-red-300'
          }`}>
            <span className="text-lg mr-2">
              {cloudStatus === 'connected' ? '☁️' : 
               cloudStatus === 'syncing' ? '🔄' : '⚠️'}
            </span>
            {cloudStatus === 'connected' ? 'متصل بالسحابة' :
             cloudStatus === 'syncing' ? 'جاري المزامنة...' :
             'غير متصل بالسحابة'}
          </div>
          
          {cloudStatus === 'disconnected' && (
            <button
              onClick={syncWithCloud}
              className="mr-4 px-4 py-2 bg-blue-500 bg-opacity-80 hover:bg-opacity-100 text-white rounded-full text-sm font-medium transition-all duration-300"
            >
              🔄 إعادة المحاولة
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="bg-red-500 bg-opacity-20 border border-red-500 text-red-300 p-4 rounded-lg mb-6">
          ❌ {error}
          <button 
            onClick={() => setError('')}
            className="mr-4 text-red-200 hover:text-white"
          >
            إغلاق
          </button>
        </div>
      )}

      {loading && (
        <div className="text-center mb-8">
          <div className="inline-flex items-center px-6 py-3 bg-blue-500 bg-opacity-20 rounded-2xl">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-300 mr-3"></div>
            <span className="text-blue-300">جاري تحميل البيانات من السحابة...</span>
          </div>
        </div>
      )}

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <div className="glass p-6 rounded-3xl text-center animate-pulse-custom">
          <div className="text-4xl mb-2">📊</div>
          <h3 className="font-bold text-lg text-white mb-1">إجمالي العملاء</h3>
          <p className="text-2xl font-bold text-blue-300">{totalInstallments}</p>
        </div>

        <div className="glass p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">⏰</div>
          <h3 className="font-bold text-lg text-white mb-1">أقساط نشطة</h3>
          <p className="text-2xl font-bold text-green-300">{activeInstallments}</p>
        </div>

        <div className="glass p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">✅</div>
          <h3 className="font-bold text-lg text-white mb-1">أقساط مكتملة</h3>
          <p className="text-2xl font-bold text-blue-300">{completedInstallments}</p>
        </div>

        <div className="glass p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">💰</div>
          <h3 className="font-bold text-lg text-white mb-1">إجمالي المبالغ</h3>
          <p className="text-xl font-bold text-yellow-300">{totalRevenue.toFixed(2)} د.ع</p>
        </div>

        <div className="glass p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">📈</div>
          <h3 className="font-bold text-lg text-white mb-1">إجمالي الفوائد</h3>
          <p className="text-xl font-bold text-green-300">{totalInterestEarned.toFixed(2)} د.ع</p>
        </div>
      </div>

      {/* زر إضافة عميل جديد */}
      <div className="text-center mb-8">
        <button
          onClick={() => setShowAddForm(!showAddForm)}
          className="btn-gradient px-8 py-4 rounded-2xl text-white font-bold text-lg transform hover:scale-105 transition-all duration-300 shadow-glow"
        >
          <span className="flex items-center justify-center">
            <span className="text-xl ml-2">{showAddForm ? '❌' : '➕'}</span>
            {showAddForm ? 'إلغاء' : 'إضافة عميل جديد'}
          </span>
        </button>
      </div>

      {/* نموذج إضافة/تحرير عميل */}
      {showAddForm && (
        <div className="glass p-8 rounded-3xl shadow-glow mb-8 animate-slideInRight">
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
            <span className="text-3xl ml-3">{editingId ? '✏️' : '➕'}</span>
            {editingId ? 'تحرير بيانات العميل' : 'إضافة عميل جديد'}
            <span className="text-lg ml-3">☁️</span>
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-white font-medium mb-2">اسم العميل *</label>
              <input
                placeholder="أدخل اسم العميل"
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
                value={form.customerName}
                onChange={(e) => setForm({ ...form, customerName: e.target.value })}
                required
              />
            </div>

            <div>
              <label className="block text-white font-medium mb-2">رقم الهاتف *</label>
              <input
                placeholder="أدخل رقم الهاتف"
                type="tel"
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
                value={form.customerPhone}
                onChange={(e) => setForm({ ...form, customerPhone: e.target.value })}
                required
              />
            </div>

            <div>
              <label className="block text-white font-medium mb-2">مبلغ القسط الكامل *</label>
              <input
                placeholder="أدخل المبلغ الأساسي"
                type="number"
                step="0.01"
                min="0"
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
                value={form.totalAmount}
                onChange={(e) => setForm({ ...form, totalAmount: e.target.value })}
                required
              />
            </div>

            <div>
              <label className="block text-white font-medium mb-2">الفائدة (%) *</label>
              <input
                placeholder="أدخل نسبة الفائدة"
                type="number"
                step="0.1"
                min="0"
                max="100"
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
                value={form.interestRate}
                onChange={(e) => setForm({ ...form, interestRate: e.target.value })}
                required
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-white font-medium mb-2">عدد الأشهر *</label>
              <input
                placeholder="أدخل عدد الأشهر"
                type="number"
                min="1"
                max="120"
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
                value={form.monthsCount}
                onChange={(e) => setForm({ ...form, monthsCount: e.target.value })}
                required
              />
            </div>
          </div>

          {/* معاينة حساب القسط */}
          {form.totalAmount && form.interestRate && form.monthsCount && (
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 bg-opacity-20 p-6 rounded-2xl mb-6 border border-blue-500 border-opacity-30">
              <h3 className="text-blue-300 font-bold mb-4 text-center">📊 معاينة الحساب التفصيلية</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-center mb-4">
                <div className="bg-white bg-opacity-10 p-4 rounded-xl">
                  <p className="text-white text-opacity-80 text-sm">💰 المبلغ الأساسي</p>
                  <p className="text-blue-300 font-bold text-lg">{calculateInstallmentDetails().principal} د.ع</p>
                </div>
                <div className="bg-white bg-opacity-10 p-4 rounded-xl">
                  <p className="text-white text-opacity-80 text-sm">🔢 الفائدة</p>
                  <p className="text-orange-300 font-bold text-lg">{calculateInstallmentDetails().interestAmount} د.ع</p>
                </div>
                <div className="bg-white bg-opacity-10 p-4 rounded-xl">
                  <p className="text-white text-opacity-80 text-sm">💸 المبلغ مع الفائدة</p>
                  <p className="text-green-300 font-bold text-lg">{calculateInstallmentDetails().totalAmountWithInterest} د.ع</p>
                </div>
                <div className="bg-white bg-opacity-10 p-4 rounded-xl">
                  <p className="text-white text-opacity-80 text-sm">💳 القسط الشهري</p>
                  <p className="text-purple-300 font-bold text-lg">{calculateInstallmentDetails().monthlyPayment} د.ع</p>
                </div>
              </div>

              <div className="bg-white bg-opacity-5 p-4 rounded-xl text-center">
                <p className="text-white text-opacity-70 text-sm">
                  📝 <strong>الحساب:</strong>
                  الفائدة = {form.totalAmount} × {form.interestRate}% = {calculateInstallmentDetails().interestAmount} د.ع |
                  المبلغ مع الفائدة = {form.totalAmount} + {calculateInstallmentDetails().interestAmount} = {calculateInstallmentDetails().totalAmountWithInterest} د.ع |
                  القسط الشهري = {calculateInstallmentDetails().totalAmountWithInterest} ÷ {form.monthsCount} = {calculateInstallmentDetails().monthlyPayment} د.ع
                </p>
              </div>
            </div>
          )}

          <div className="flex gap-4">
            <button
              onClick={addOrUpdateInstallment}
              disabled={!form.customerName || !form.totalAmount || !form.interestRate || !form.monthsCount || !form.customerPhone || cloudStatus === 'syncing'}
              className="flex-1 btn-gradient px-8 py-4 rounded-2xl text-white font-bold text-lg transform hover:scale-105 transition-all duration-300 shadow-glow disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              <span className="flex items-center justify-center">
                <span className="text-xl ml-2">{cloudStatus === 'syncing' ? '🔄' : (editingId ? '✅' : '➕')}</span>
                {cloudStatus === 'syncing' ? 'جاري الحفظ...' : (editingId ? 'حفظ التعديلات' : 'إضافة العميل')}
                <span className="text-lg ml-2">☁️</span>
              </span>
            </button>

            <button
              onClick={resetForm}
              className="px-6 py-4 rounded-2xl text-white font-bold text-lg bg-gray-500 bg-opacity-80 hover:bg-opacity-100 transform hover:scale-105 transition-all duration-300"
            >
              <span className="flex items-center justify-center">
                <span className="text-xl ml-2">❌</span>
                إلغاء
              </span>
            </button>
          </div>
        </div>
      )}

      {/* باقي المكون - قائمة العملاء والنوافذ المنبثقة */}
      <div className="text-center mt-8">
        <div className="glass p-6 rounded-3xl">
          <h3 className="text-xl font-bold text-white mb-4">☁️ معلومات الحفظ السحابي</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-white text-opacity-80">
            <div>
              <p className="font-medium">💾 طريقة الحفظ</p>
              <p className="text-blue-300">Firebase Cloud (سحابي)</p>
            </div>
            <div>
              <p className="font-medium">🔄 حالة المزامنة</p>
              <p className={
                cloudStatus === 'connected' ? 'text-green-300' :
                cloudStatus === 'syncing' ? 'text-yellow-300' :
                'text-red-300'
              }>
                {cloudStatus === 'connected' ? 'متزامن' :
                 cloudStatus === 'syncing' ? 'جاري المزامنة' :
                 'غير متزامن'}
              </p>
            </div>
            <div>
              <p className="font-medium">🛡️ النسخ الاحتياطية</p>
              <p className="text-green-300">محلية + سحابية</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstallmentsCloud;
