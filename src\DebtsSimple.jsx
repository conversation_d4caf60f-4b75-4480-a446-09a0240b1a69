import React, { useState, useEffect } from 'react';

const DebtsSimple = () => {
  const [debts, setDebts] = useState([]);
  const [form, setForm] = useState({ customerName: '', amount: '', date: '' });
  const [error, setError] = useState('');

  // تحميل البيانات من localStorage عند بدء التطبيق
  useEffect(() => {
    console.log('🔄 تحميل الديون من localStorage...');
    const savedDebts = localStorage.getItem('simple_debts');
    if (savedDebts) {
      try {
        const parsedDebts = JSON.parse(savedDebts);
        setDebts(parsedDebts);
        console.log('✅ تم تحميل', parsedDebts.length, 'دين من localStorage');
      } catch (error) {
        console.error('❌ خطأ في تحليل البيانات المحفوظة:', error);
      }
    } else {
      console.log('ℹ️ لا توجد ديون محفوظة');
    }
  }, []);

  const addDebt = () => {
    console.log('➕ محاولة إضافة دين جديد...');
    
    if (!form.customerName || !form.amount || !form.date) {
      setError('يرجى ملء جميع الحقول');
      console.log('❌ حقول مفقودة');
      return;
    }

    const newDebt = {
      id: Date.now(),
      customerName: form.customerName,
      amount: parseFloat(form.amount),
      date: form.date,
      createdAt: new Date().toISOString()
    };

    console.log('📝 دين جديد:', newDebt);

    const updatedDebts = [newDebt, ...debts];
    setDebts(updatedDebts);
    
    // حفظ في localStorage
    try {
      localStorage.setItem('simple_debts', JSON.stringify(updatedDebts));
      console.log('💾 تم حفظ الديون في localStorage');
    } catch (error) {
      console.error('❌ فشل في حفظ البيانات:', error);
      setError('فشل في حفظ البيانات محلياً');
    }

    setForm({ customerName: '', amount: '', date: '' });
    setError('');
    console.log('✅ تم إضافة الدين بنجاح');
  };

  const deleteDebt = (id) => {
    console.log('🗑️ محاولة حذف الدين:', id);
    
    if (window.confirm('هل تريد حذف هذا الدين؟')) {
      const updatedDebts = debts.filter(d => d.id !== id);
      setDebts(updatedDebts);
      
      try {
        localStorage.setItem('simple_debts', JSON.stringify(updatedDebts));
        console.log('✅ تم حذف الدين وحفظ التغييرات');
      } catch (error) {
        console.error('❌ فشل في حفظ التغييرات:', error);
      }
    }
  };

  const clearAllDebts = () => {
    if (window.confirm('هل تريد حذف جميع الديون؟')) {
      setDebts([]);
      localStorage.removeItem('simple_debts');
      console.log('🧹 تم حذف جميع الديون');
    }
  };

  const totalDebts = debts.reduce((sum, debt) => sum + debt.amount, 0);

  return (
    <div className="p-6 max-w-5xl mx-auto min-h-screen">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-white mb-2">💳 الديون البسيطة</h1>
        <p className="text-white text-opacity-80">نسخة مبسطة للاختبار</p>
      </div>

      {error && (
        <div className="bg-red-500 bg-opacity-20 border border-red-500 text-red-300 p-4 rounded-lg mb-6">
          ❌ {error}
          <button 
            onClick={() => setError('')}
            className="mr-4 text-red-200 hover:text-white"
          >
            إغلاق
          </button>
        </div>
      )}

      <div className="bg-white bg-opacity-10 p-8 rounded-3xl mb-8 text-center">
        <h2 className="font-bold text-2xl text-white mb-2">إجمالي الديون</h2>
        <p className="text-4xl font-bold text-yellow-300">{totalDebts.toFixed(2)} دينار</p>
        <p className="text-white text-opacity-70 mt-2">{debts.length} دين</p>
      </div>

      <div className="bg-white bg-opacity-10 p-8 rounded-3xl mb-8">
        <h2 className="text-2xl font-bold text-white mb-6">إضافة دين جديد</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <input
            placeholder="اسم العميل"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={form.customerName}
            onChange={(e) => setForm({ ...form, customerName: e.target.value })}
          />
          <input
            placeholder="المبلغ"
            type="number"
            step="0.01"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={form.amount}
            onChange={(e) => setForm({ ...form, amount: e.target.value })}
          />
          <input
            type="date"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={form.date}
            onChange={(e) => setForm({ ...form, date: e.target.value })}
          />
        </div>

        <div className="flex gap-4">
          <button
            onClick={addDebt}
            className="bg-gradient-to-r from-blue-500 to-purple-600 px-8 py-4 rounded-2xl text-white font-bold text-lg hover:scale-105 transition-all duration-300"
          >
            ➕ إضافة دين
          </button>
          
          <button
            onClick={clearAllDebts}
            className="bg-red-500 hover:bg-red-600 px-6 py-4 rounded-2xl text-white font-bold text-lg transition-all duration-300"
          >
            🧹 حذف الكل
          </button>
        </div>
      </div>

      <div className="space-y-6">
        {debts.length === 0 ? (
          <div className="bg-white bg-opacity-10 p-12 rounded-3xl text-center text-white text-opacity-60">
            <div className="text-6xl mb-4">💳</div>
            <p className="text-xl mb-2">لا توجد ديون مسجلة</p>
            <p className="text-sm opacity-80">ابدأ بإضافة دين جديد</p>
          </div>
        ) : (
          debts.map((debt) => (
            <div key={debt.id} className="bg-white bg-opacity-10 p-6 rounded-3xl flex justify-between items-center hover:bg-opacity-20 transition-all duration-300">
              <div className="text-white">
                <h3 className="font-bold text-xl mb-2">👤 {debt.customerName}</h3>
                <p className="text-lg mb-1">💰 المبلغ: {debt.amount} دينار</p>
                <p className="text-sm opacity-80">📅 التاريخ: {debt.date}</p>
                <p className="text-xs opacity-60">🕒 أُضيف: {new Date(debt.createdAt).toLocaleString('ar-SA')}</p>
              </div>
              <button
                onClick={() => deleteDebt(debt.id)}
                className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-2xl font-medium transition-all duration-300 hover:scale-105"
              >
                🗑️ حذف
              </button>
            </div>
          ))
        )}
      </div>

      <div className="text-center mt-8">
        <p className="text-white text-opacity-60 text-sm">
          البيانات محفوظة في localStorage
        </p>
      </div>
    </div>
  );
};

export default DebtsSimple;
