import React, { useState, useEffect } from 'react';

const InventoryTest = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(false);

  // تحميل المنتجات من localStorage
  useEffect(() => {
    const savedProducts = localStorage.getItem('products');
    if (savedProducts) {
      try {
        const parsedProducts = JSON.parse(savedProducts);
        setProducts(parsedProducts);
        console.log('✅ تم تحميل', parsedProducts.length, 'منتج من localStorage');
      } catch (error) {
        console.error('❌ خطأ في تحليل البيانات:', error);
      }
    } else {
      // إنشاء منتجات تجريبية
      const testProducts = [
        {
          id: 'test1',
          name: 'منتج تجريبي 1',
          purchasePrice: 100,
          salePrice: 150,
          quantity: 10,
          category: 'تجريبي'
        },
        {
          id: 'test2',
          name: 'منتج تجريبي 2',
          purchasePrice: 200,
          salePrice: 300,
          quantity: 5,
          category: 'تجريبي'
        }
      ];
      setProducts(testProducts);
      localStorage.setItem('products', JSON.stringify(testProducts));
      console.log('✅ تم إنشاء منتجات تجريبية');
    }
  }, []);

  const deleteProduct = (id) => {
    console.log('🗑️ محاولة حذف المنتج:', id);
    
    if (!window.confirm('❗ هل تريد حذف هذا المنتج؟')) {
      console.log('❌ تم إلغاء الحذف');
      return;
    }

    setLoading(true);
    
    try {
      const productToDelete = products.find(p => p.id === id);
      console.log('📦 المنتج المراد حذفه:', productToDelete);
      
      const updatedProducts = products.filter(p => p.id !== id);
      setProducts(updatedProducts);
      
      localStorage.setItem('products', JSON.stringify(updatedProducts));
      
      console.log('✅ تم حذف المنتج بنجاح');
      alert(`✅ تم حذف المنتج "${productToDelete.name}" بنجاح`);
      
    } catch (error) {
      console.error('❌ خطأ في حذف المنتج:', error);
      alert('❌ فشل في حذف المنتج');
    } finally {
      setLoading(false);
    }
  };

  const addTestProduct = () => {
    const newProduct = {
      id: 'test' + Date.now(),
      name: 'منتج جديد ' + Date.now(),
      purchasePrice: 50,
      salePrice: 75,
      quantity: 3,
      category: 'تجريبي'
    };
    
    const updatedProducts = [...products, newProduct];
    setProducts(updatedProducts);
    localStorage.setItem('products', JSON.stringify(updatedProducts));
    
    console.log('✅ تم إضافة منتج جديد:', newProduct);
  };

  return (
    <div className="p-6 max-w-5xl mx-auto min-h-screen">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-white mb-2">🧪 اختبار المخزون</h1>
        <p className="text-white text-opacity-80">اختبار وظائف الحذف والإضافة</p>
      </div>

      <div className="glass p-6 rounded-3xl mb-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white">المنتجات ({products.length})</h2>
          <button
            onClick={addTestProduct}
            className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-2xl font-bold transition-all duration-300"
          >
            ➕ إضافة منتج تجريبي
          </button>
        </div>

        {products.length === 0 ? (
          <div className="text-center text-white text-opacity-60 py-12">
            <div className="text-6xl mb-4">📦</div>
            <p className="text-xl">لا توجد منتجات</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {products.map((product) => (
              <div key={product.id} className="bg-white bg-opacity-10 p-6 rounded-2xl backdrop-blur-sm">
                <h3 className="text-white font-bold text-lg mb-4">{product.name}</h3>
                
                <div className="space-y-2 text-white text-opacity-80 mb-6">
                  <p>💰 سعر الشراء: {product.purchasePrice} د.ع</p>
                  <p>💵 سعر البيع: {product.salePrice} د.ع</p>
                  <p>📦 الكمية: {product.quantity}</p>
                  <p>🏷️ الفئة: {product.category}</p>
                  <p className="text-xs opacity-60">🆔 المعرف: {product.id}</p>
                </div>

                <button
                  onClick={() => deleteProduct(product.id)}
                  disabled={loading}
                  className="w-full bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? '⏳ جاري الحذف...' : '🗑️ حذف'}
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="glass p-6 rounded-3xl">
        <h3 className="text-xl font-bold text-white mb-4">📊 معلومات التشخيص</h3>
        <div className="text-white text-opacity-80 space-y-2">
          <p>📦 عدد المنتجات: {products.length}</p>
          <p>💾 حالة localStorage: {localStorage.getItem('products') ? 'موجود' : 'فارغ'}</p>
          <p>⏳ حالة التحميل: {loading ? 'نشط' : 'متوقف'}</p>
        </div>
        
        <div className="mt-4">
          <button
            onClick={() => console.log('المنتجات الحالية:', products)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-xl mr-2"
          >
            📋 طباعة المنتجات في Console
          </button>
          
          <button
            onClick={() => {
              localStorage.removeItem('products');
              setProducts([]);
              console.log('🧹 تم مسح localStorage');
            }}
            className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-xl"
          >
            🧹 مسح localStorage
          </button>
        </div>
      </div>
    </div>
  );
};

export default InventoryTest;
