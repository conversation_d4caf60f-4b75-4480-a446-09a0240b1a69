// اختبار بسيط للتأكد من أن Firebase يعمل
import firebaseService from './src/services/firebaseService.js';

async function testFirebase() {
  try {
    console.log('🔥 اختبار Firebase...');
    
    // اختبار إنشاء دين
    const testDebt = {
      customerName: 'عميل تجريبي',
      amount: 100,
      originalAmount: 100,
      debtDate: new Date(),
      status: 'pending',
      notes: 'دين تجريبي للاختبار'
    };
    
    console.log('📝 إنشاء دين تجريبي...');
    const createdDebt = await firebaseService.createDebt(testDebt);
    console.log('✅ تم إنشاء الدين:', createdDebt);
    
    // اختبار جلب الديون
    console.log('📋 جلب جميع الديون...');
    const debts = await firebaseService.getDebts();
    console.log('✅ تم جلب الديون:', debts.length, 'دين');
    
    // اختبار حذف الدين التجريبي
    if (createdDebt.id) {
      console.log('🗑️ حذف الدين التجريبي...');
      await firebaseService.deleteDebt(createdDebt.id);
      console.log('✅ تم حذف الدين التجريبي');
    }
    
    console.log('🎉 جميع الاختبارات نجحت!');
    
  } catch (error) {
    console.error('❌ فشل الاختبار:', error);
  }
}

// تشغيل الاختبار
testFirebase();
