import express from 'express';
import Sale from '../models/Sale.js';
import Product from '../models/Product.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Get all sales
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { startDate, endDate, customer, paymentMethod } = req.query;
    let query = {};

    if (startDate || endDate) {
      query.saleDate = {};
      if (startDate) query.saleDate.$gte = new Date(startDate);
      if (endDate) query.saleDate.$lte = new Date(endDate);
    }

    if (customer) {
      query.customer = { $regex: customer, $options: 'i' };
    }

    if (paymentMethod) {
      query.paymentMethod = paymentMethod;
    }

    const sales = await Sale.find(query)
      .populate('items.productId', 'name category')
      .sort({ createdAt: -1 });

    res.json(sales);
  } catch (error) {
    console.error('Get sales error:', error);
    res.status(500).json({ message: 'خطأ في جلب المبيعات' });
  }
});

// Get sale by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const sale = await Sale.findById(req.params.id)
      .populate('items.productId', 'name category');
    
    if (!sale) {
      return res.status(404).json({ message: 'البيع غير موجود' });
    }
    
    res.json(sale);
  } catch (error) {
    console.error('Get sale error:', error);
    res.status(500).json({ message: 'خطأ في جلب البيع' });
  }
});

// Create sale
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { items, discount = 0, customer, paymentMethod = 'نقدي' } = req.body;

    if (!items || items.length === 0) {
      return res.status(400).json({ message: 'يجب إضافة منتجات للبيع' });
    }

    // Verify product availability and update quantities
    for (const item of items) {
      const product = await Product.findById(item.productId);
      
      if (!product) {
        return res.status(404).json({ message: `المنتج ${item.name} غير موجود` });
      }

      if (product.quantity < item.quantity) {
        return res.status(400).json({ 
          message: `الكمية المتاحة من ${product.name}: ${product.quantity} فقط` 
        });
      }

      // Update product quantity
      product.quantity -= item.quantity;
      await product.save();
    }

    // Create sale
    const sale = new Sale({
      items,
      discount,
      customer,
      paymentMethod
    });

    await sale.save();
    await sale.populate('items.productId', 'name category');

    res.status(201).json(sale);
  } catch (error) {
    console.error('Create sale error:', error);
    res.status(400).json({ message: 'خطأ في إنشاء البيع', error: error.message });
  }
});

// Cancel sale
router.patch('/:id/cancel', authenticateToken, async (req, res) => {
  try {
    const sale = await Sale.findById(req.params.id);
    
    if (!sale) {
      return res.status(404).json({ message: 'البيع غير موجود' });
    }

    if (sale.status !== 'completed') {
      return res.status(400).json({ message: 'لا يمكن إلغاء هذا البيع' });
    }

    // Restore product quantities
    for (const item of sale.items) {
      const product = await Product.findById(item.productId);
      if (product) {
        product.quantity += item.quantity;
        await product.save();
      }
    }

    sale.status = 'cancelled';
    await sale.save();

    res.json({ message: 'تم إلغاء البيع وإرجاع الكميات للمخزون', sale });
  } catch (error) {
    console.error('Cancel sale error:', error);
    res.status(500).json({ message: 'خطأ في إلغاء البيع' });
  }
});

// Delete sale
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const sale = await Sale.findById(req.params.id);
    
    if (!sale) {
      return res.status(404).json({ message: 'البيع غير موجود' });
    }

    // If sale is completed, restore quantities
    if (sale.status === 'completed') {
      for (const item of sale.items) {
        const product = await Product.findById(item.productId);
        if (product) {
          product.quantity += item.quantity;
          await product.save();
        }
      }
    }

    await Sale.findByIdAndDelete(req.params.id);
    res.json({ message: 'تم حذف البيع بنجاح' });
  } catch (error) {
    console.error('Delete sale error:', error);
    res.status(500).json({ message: 'خطأ في حذف البيع' });
  }
});

// Get sales statistics
router.get('/stats/summary', authenticateToken, async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const [totalSales, todaySales, salesCount, todaySalesCount] = await Promise.all([
      Sale.aggregate([
        { $match: { status: 'completed' } },
        { $group: { _id: null, total: { $sum: '$total' } } }
      ]),
      Sale.aggregate([
        { 
          $match: { 
            status: 'completed',
            saleDate: { $gte: today, $lt: tomorrow }
          } 
        },
        { $group: { _id: null, total: { $sum: '$total' } } }
      ]),
      Sale.countDocuments({ status: 'completed' }),
      Sale.countDocuments({ 
        status: 'completed',
        saleDate: { $gte: today, $lt: tomorrow }
      })
    ]);

    const stats = {
      totalSales: totalSales[0]?.total || 0,
      todaySales: todaySales[0]?.total || 0,
      salesCount,
      todaySalesCount,
      averageSale: salesCount > 0 ? (totalSales[0]?.total || 0) / salesCount : 0
    };

    res.json(stats);
  } catch (error) {
    console.error('Get sales stats error:', error);
    res.status(500).json({ message: 'خطأ في جلب إحصائيات المبيعات' });
  }
});

export default router;
