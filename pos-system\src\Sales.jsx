import React, { useState, useEffect } from 'react';
import apiService from './services/api';

const Sales = () => {
  const [cart, setCart] = useState([]);
  const [discount, setDiscount] = useState(0);
  const [products, setProducts] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState('');
  const [quantity, setQuantity] = useState(1);
  const [customerName, setCustomerName] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('نقدي');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      const data = await apiService.getProducts();
      setProducts(data);
      console.log('✅ تم تحميل المنتجات من الخادم');
    } catch (error) {
      console.log('⚠️ فشل تحميل المنتجات من الخادم، استخدام التخزين المحلي');
      // Fallback to localStorage if API fails
      const savedProducts = JSON.parse(localStorage.getItem('products') || '[]');
      setProducts(savedProducts);
    }
  };

  const addToCart = () => {
    if (!selectedProduct || quantity <= 0) return;

    const product = products.find(p => p.id === parseInt(selectedProduct));
    if (!product) return;

    // التحقق من توفر الكمية
    if (product.quantity < quantity) {
      alert(`❗ الكمية المتاحة: ${product.quantity} فقط`);
      return;
    }

    // التحقق من وجود المنتج في السلة
    const existingItem = cart.find(item => item.productId === product.id);
    if (existingItem) {
      const totalQuantity = existingItem.quantity + parseInt(quantity);
      if (totalQuantity > product.quantity) {
        alert(`❗ الكمية الإجمالية تتجاوز المتاح (${product.quantity})`);
        return;
      }

      setCart(cart.map(item =>
        item.productId === product.id
          ? { ...item, quantity: totalQuantity, total: item.price * totalQuantity }
          : item
      ));
    } else {
      const newItem = {
        id: Date.now(),
        productId: product.id,
        name: product.name,
        price: product.salePrice,
        quantity: parseInt(quantity),
        total: product.salePrice * parseInt(quantity),
        category: product.category
      };
      setCart([...cart, newItem]);
    }

    setSelectedProduct('');
    setQuantity(1);
  };

  const removeFromCart = (id) => {
    setCart(cart.filter(item => item.id !== id));
  };

  const total = cart.reduce((sum, item) => sum + item.total, 0);
  const finalTotal = total - discount;

  const completeSale = async () => {
    if (cart.length === 0) {
      alert('❗ السلة فارغة!');
      return;
    }

    try {
      setLoading(true);

      // تحضير بيانات البيع
      const saleData = {
        items: cart.map(item => ({
          productId: item.productId,
          name: item.name,
          price: item.price,
          quantity: item.quantity,
          total: item.total,
          category: item.category
        })),
        discount,
        customer: customerName,
        paymentMethod
      };

      try {
        // محاولة إرسال البيع للخادم
        await apiService.createSale(saleData);
        alert('✅ تم إتمام البيع بنجاح في الخادم! تم تحديث المخزون.');

        // إعادة تحميل المنتجات لتحديث الكميات
        await loadProducts();

      } catch (apiError) {
        console.log('API failed, using localStorage fallback');

        // Fallback: حفظ البيع محلياً
        // تحديث كميات المنتجات في المخزون المحلي
        const updatedProducts = products.map(product => {
          const cartItem = cart.find(item => item.productId === product.id);
          if (cartItem) {
            return {
              ...product,
              quantity: product.quantity - cartItem.quantity
            };
          }
          return product;
        });

        // حفظ المخزون المحدث
        localStorage.setItem('products', JSON.stringify(updatedProducts));
        setProducts(updatedProducts);

        const sale = {
          id: Date.now(),
          items: cart,
          subtotal: total,
          discount,
          total: finalTotal,
          customer: customerName,
          paymentMethod,
          saleDate: new Date().toISOString(),
          date: new Date().toLocaleDateString('ar-EG'),
          time: new Date().toLocaleTimeString('ar-EG')
        };

        // حفظ البيع في localStorage
        const sales = JSON.parse(localStorage.getItem('sales') || '[]');
        sales.push(sale);
        localStorage.setItem('sales', JSON.stringify(sales));

        alert('✅ تم إتمام البيع بنجاح محلياً! تم تحديث المخزون.');
      }

      // إعادة تعيين النموذج
      setCart([]);
      setDiscount(0);
      setCustomerName('');
      setPaymentMethod('نقدي');

    } catch (error) {
      console.error('Error completing sale:', error);
      alert('❌ حدث خطأ في إتمام البيع: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const getFilteredProducts = () => {
    return products.filter(product =>
      product.quantity > 0 &&
      product.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  return (
    <div className="p-6 max-w-6xl mx-auto min-h-screen">
      <div className="text-center mb-8 animate-fadeInUp">
        <h1 className="text-4xl font-bold text-gradient mb-2">📦 نقطة البيع</h1>
        <p className="text-white text-opacity-80">إدارة المبيعات والفواتير</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* قسم إضافة المنتجات */}
        <div className="glass p-6 rounded-3xl shadow-glow animate-slideInRight card-hover">
          <h2 className="text-xl font-bold mb-6 text-white flex items-center">
            <span className="text-2xl ml-3">➕</span>
            إضافة منتج من المخزون
          </h2>

          <div className="space-y-4">
            <input
              placeholder="🔍 البحث عن منتج..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            />

            <select
              value={selectedProduct}
              onChange={(e) => setSelectedProduct(e.target.value)}
              className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white transition-all duration-300 focus:outline-none backdrop-blur-sm"
            >
              <option value="" className="text-gray-800">اختر منتج من المخزون</option>
              {getFilteredProducts().map((product) => (
                <option key={product.id} value={product.id} className="text-gray-800">
                  {product.name} - {product.salePrice} د.ع (متوفر: {product.quantity})
                </option>
              ))}
            </select>

            <input
              type="number"
              placeholder="الكمية"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
              min="1"
              max={selectedProduct ? products.find(p => p.id === parseInt(selectedProduct))?.quantity : 1}
            />

            <button
              onClick={addToCart}
              className="btn-gradient w-full py-4 rounded-2xl text-white font-bold transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              disabled={!selectedProduct || quantity <= 0}
            >
              <span className="flex items-center justify-center">
                <span className="text-xl ml-2">➕</span>
                إضافة للسلة
              </span>
            </button>
          </div>

          {/* عرض المنتجات المتاحة */}
          {products.length === 0 && (
            <div className="mt-6 p-4 bg-yellow-500 bg-opacity-20 border border-yellow-400 rounded-2xl text-center text-white animate-fadeInUp">
              <div className="text-3xl mb-2">⚠️</div>
              <p className="font-medium">لا توجد منتجات في المخزون</p>
              <p className="text-sm opacity-80">اذهب إلى صفحة المخزون لإضافة منتجات</p>
            </div>
          )}
        </div>

        {/* قسم السلة */}
        <div className="glass p-6 rounded-3xl shadow-glow animate-slideInRight card-hover" style={{ animationDelay: '0.2s' }}>
          <h2 className="text-xl font-bold mb-6 text-white flex items-center">
            <span className="text-2xl ml-3">🛒</span>
            السلة ({cart.length} عنصر)
          </h2>

          <div className="max-h-80 overflow-y-auto mb-6 space-y-3">
            {cart.length === 0 ? (
              <div className="text-center py-12 text-white text-opacity-60">
                <div className="text-4xl mb-4">🛒</div>
                <p className="text-lg">السلة فارغة</p>
                <p className="text-sm opacity-80">أضف منتجات لبدء البيع</p>
              </div>
            ) : (
              cart.map((item, index) => (
                <div key={item.id} className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-4 flex justify-between items-center animate-fadeInUp card-hover" style={{ animationDelay: `${index * 0.1}s` }}>
                  <div className="text-white">
                    <span className="font-bold text-lg">{item.name}</span>
                    <br />
                    <span className="text-sm opacity-80">
                      {item.quantity} × {item.price} = {item.total} د.ع
                    </span>
                    <br />
                    <span className="text-xs opacity-60 bg-white bg-opacity-20 px-2 py-1 rounded-full">
                      {item.category}
                    </span>
                  </div>
                  <button
                    onClick={() => removeFromCart(item.id)}
                    className="bg-red-500 bg-opacity-20 hover:bg-opacity-40 text-red-300 hover:text-red-100 p-2 rounded-xl transition-all duration-300 transform hover:scale-110"
                  >
                    🗑️
                  </button>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* عرض سريع للمنتجات المتاحة */}
      {getFilteredProducts().length > 0 && (
        <div className="glass p-6 rounded-3xl shadow-glow mt-8 animate-fadeInUp">
          <h2 className="text-xl font-bold mb-6 text-white flex items-center">
            <span className="text-2xl ml-3">📦</span>
            المنتجات المتاحة
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-80 overflow-y-auto">
            {getFilteredProducts().slice(0, 6).map((product, index) => (
              <div
                key={product.id}
                className="bg-white bg-opacity-10 backdrop-blur-sm p-4 rounded-2xl cursor-pointer hover:bg-opacity-20 transition-all duration-300 transform hover:scale-105 card-hover animate-slideInRight"
                style={{ animationDelay: `${index * 0.1}s` }}
                onClick={() => {
                  setSelectedProduct(product.id.toString());
                  setQuantity(1);
                }}
              >
                <h3 className="font-bold text-white text-lg mb-2">{product.name}</h3>
                <p className="text-green-300 font-bold text-xl">{product.salePrice} د.ع</p>
                <p className="text-white text-opacity-70 text-sm">متوفر: {product.quantity}</p>
                <p className="text-blue-300 text-sm bg-white bg-opacity-20 px-2 py-1 rounded-full inline-block mt-2">{product.category}</p>
              </div>
            ))}
          </div>
          {getFilteredProducts().length > 6 && (
            <p className="text-center text-white text-opacity-60 text-sm mt-4">
              وأكثر من {getFilteredProducts().length - 6} منتج آخر...
            </p>
          )}
        </div>
      )}

      {/* قسم الفاتورة */}
      <div className="glass p-8 rounded-3xl shadow-glow mt-8 animate-fadeInUp">
        <h2 className="text-2xl font-bold mb-6 text-white flex items-center">
          <span className="text-3xl ml-3">🧾</span>
          تفاصيل الفاتورة
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <input
            placeholder="اسم العميل (اختياري)"
            value={customerName}
            onChange={(e) => setCustomerName(e.target.value)}
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
          />

          <select
            value={paymentMethod}
            onChange={(e) => setPaymentMethod(e.target.value)}
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white transition-all duration-300 focus:outline-none backdrop-blur-sm"
          >
            <option value="نقدي" className="text-gray-800">💵 نقدي</option>
            <option value="بطاقة" className="text-gray-800">💳 بطاقة</option>
            <option value="تحويل" className="text-gray-800">🏦 تحويل بنكي</option>
          </select>

          <input
            type="number"
            placeholder="خصم على الفاتورة"
            value={discount}
            onChange={(e) => setDiscount(parseFloat(e.target.value) || 0)}
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
          />
        </div>

        <div className="bg-white bg-opacity-10 backdrop-blur-sm p-6 rounded-3xl mb-6 animate-pulse-custom">
          <div className="flex justify-between text-lg text-white mb-3">
            <span className="flex items-center">
              <span className="text-xl ml-2">💰</span>
              المجموع الفرعي:
            </span>
            <span className="font-bold">{total.toFixed(2)} د.ع</span>
          </div>
          <div className="flex justify-between text-lg text-white mb-3">
            <span className="flex items-center">
              <span className="text-xl ml-2">📉</span>
              الخصم:
            </span>
            <span className="font-bold">-{discount.toFixed(2)} د.ع</span>
          </div>
          <hr className="my-4 border-white border-opacity-30" />
          <div className="flex justify-between text-2xl font-bold text-green-300">
            <span className="flex items-center">
              <span className="text-3xl ml-2">✅</span>
              المجموع النهائي:
            </span>
            <span className="text-gradient">{finalTotal.toFixed(2)} د.ع</span>
          </div>
        </div>

        <button
          onClick={completeSale}
          className="btn-gradient w-full py-5 rounded-3xl text-white text-xl font-bold transform hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none shadow-glow-green"
          disabled={cart.length === 0}
        >
          <span className="flex items-center justify-center">
            <span className="text-2xl ml-3">✅</span>
            إتمام البيع
          </span>
        </button>
      </div>
    </div>
  );
};

export default Sales;
