{"name": "pos-app", "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "cd server && npm run dev", "start:full": "concurrently \"npm run server\" \"npm run dev\""}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.1"}, "devDependencies": {"@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.0", "concurrently": "^8.2.2", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "vite": "^4.0.0"}}