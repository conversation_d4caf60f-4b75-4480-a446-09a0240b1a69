import { 
  collection, 
  addDoc, 
  getDocs, 
  doc, 
  updateDoc, 
  deleteDoc,
  query,
  orderBy,
  serverTimestamp 
} from 'firebase/firestore';
import { db } from '../firebase/config';

class FirebaseService {
  // Products
  async getProducts() {
    try {
      const q = query(collection(db, 'products'), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      const products = [];
      querySnapshot.forEach((doc) => {
        products.push({ id: doc.id, ...doc.data() });
      });
      return products;
    } catch (error) {
      console.error('Error getting products:', error);
      throw error;
    }
  }

  async createProduct(productData) {
    try {
      const docRef = await addDoc(collection(db, 'products'), {
        ...productData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
      return { id: docRef.id, ...productData };
    } catch (error) {
      console.error('Error creating product:', error);
      throw error;
    }
  }

  async updateProduct(id, productData) {
    try {
      const productRef = doc(db, 'products', id);
      await updateDoc(productRef, {
        ...productData,
        updatedAt: serverTimestamp()
      });
      return { id, ...productData };
    } catch (error) {
      console.error('Error updating product:', error);
      throw error;
    }
  }

  async deleteProduct(id) {
    try {
      console.log('🔥 Firebase: محاولة حذف المنتج بالمعرف:', id);

      if (!id) {
        throw new Error('معرف المنتج مطلوب');
      }

      const productRef = doc(db, 'products', id);
      console.log('📄 مرجع المنتج:', productRef.path);

      await deleteDoc(productRef);
      console.log('✅ Firebase: تم حذف المنتج بنجاح');

      return true;
    } catch (error) {
      console.error('❌ Firebase: خطأ في حذف المنتج:', error);
      console.error('❌ تفاصيل الخطأ:', {
        code: error.code,
        message: error.message,
        productId: id
      });
      throw error;
    }
  }

  // Sales
  async getSales() {
    try {
      const q = query(collection(db, 'sales'), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      const sales = [];
      querySnapshot.forEach((doc) => {
        sales.push({ id: doc.id, ...doc.data() });
      });
      return sales;
    } catch (error) {
      console.error('Error getting sales:', error);
      throw error;
    }
  }

  async createSale(saleData) {
    try {
      // Create sale record
      const saleRef = await addDoc(collection(db, 'sales'), {
        ...saleData,
        createdAt: serverTimestamp(),
        date: new Date().toLocaleDateString('ar-EG'),
        time: new Date().toLocaleTimeString('ar-EG')
      });

      // Update product quantities
      for (const item of saleData.items) {
        const products = await this.getProducts();
        const product = products.find(p => p.id === item.productId);
        if (product) {
          await this.updateProduct(item.productId, {
            ...product,
            quantity: product.quantity - item.quantity
          });
        }
      }

      return { id: saleRef.id, ...saleData };
    } catch (error) {
      console.error('Error creating sale:', error);
      throw error;
    }
  }

  async deleteSale(id) {
    try {
      // أولاً: الحصول على بيانات البيع قبل الحذف
      const saleDoc = await getDocs(query(collection(db, 'sales')));
      let saleToDelete = null;

      saleDoc.forEach((doc) => {
        if (doc.id === id) {
          saleToDelete = { id: doc.id, ...doc.data() };
        }
      });

      if (!saleToDelete) {
        throw new Error('البيع غير موجود');
      }

      // ثانياً: إعادة الكميات للمخزون
      if (saleToDelete.items && saleToDelete.items.length > 0) {
        for (const item of saleToDelete.items) {
          try {
            // البحث عن المنتج
            const products = await this.getProducts();
            const product = products.find(p => p.id === item.productId);

            if (product) {
              // إعادة الكمية للمخزون
              const newQuantity = product.quantity + item.quantity;
              await this.updateProduct(item.productId, {
                ...product,
                quantity: newQuantity
              });
              console.log(`✅ تم إعادة ${item.quantity} من ${item.name} للمخزون`);
            }
          } catch (productError) {
            console.error('Error updating product quantity:', productError);
          }
        }
      }

      // ثالثاً: حذف البيع
      await deleteDoc(doc(db, 'sales', id));
      console.log('✅ تم حذف البيع وإعادة الكميات للمخزون');
      return true;
    } catch (error) {
      console.error('Error deleting sale:', error);
      throw error;
    }
  }

  // Debts
  async getDebts() {
    try {
      const q = query(collection(db, 'debts'), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(q);
      const debts = [];
      querySnapshot.forEach((doc) => {
        debts.push({ id: doc.id, ...doc.data() });
      });
      return debts;
    } catch (error) {
      console.error('Error getting debts:', error);
      throw error;
    }
  }

  async createDebt(debtData) {
    try {
      const docRef = await addDoc(collection(db, 'debts'), {
        ...debtData,
        createdAt: serverTimestamp()
      });
      return { id: docRef.id, ...debtData };
    } catch (error) {
      console.error('Error creating debt:', error);
      throw error;
    }
  }

  async updateDebt(id, debtData) {
    try {
      const debtRef = doc(db, 'debts', id);
      await updateDoc(debtRef, {
        ...debtData,
        updatedAt: serverTimestamp()
      });
      return { id, ...debtData };
    } catch (error) {
      console.error('Error updating debt:', error);
      throw error;
    }
  }

  async deleteDebt(id) {
    try {
      await deleteDoc(doc(db, 'debts', id));
      console.log('✅ تم حذف الدين بنجاح');
      return true;
    } catch (error) {
      console.error('Error deleting debt:', error);
      throw error;
    }
  }

  // Installments Management
  async getInstallments() {
    try {
      // جلب جميع الأقساط بدون ترتيب orderBy
      const querySnapshot = await getDocs(collection(db, 'installments'));
      const installments = [];
      querySnapshot.forEach((doc) => {
        installments.push({ id: doc.id, ...doc.data() });
      });
      return installments;
    } catch (error) {
      console.error('Error getting installments:', error);
      throw error;
    }
  }

  async createInstallment(installmentData) {
    try {
      const docRef = await addDoc(collection(db, 'installments'), {
        ...installmentData,
        createdAt: serverTimestamp()
      });
      // جلب المستند بعد الإضافة للحصول على createdAt الفعلي
      const docSnap = await getDoc(docRef);
      return { id: docRef.id, ...docSnap.data() };
    } catch (error) {
      console.error('Error creating installment:', error);
      throw error;
    }
  }

  async updateInstallment(id, installmentData) {
    try {
      const installmentRef = doc(db, 'installments', id);
      await updateDoc(installmentRef, {
        ...installmentData,
        updatedAt: serverTimestamp()
      });
      return { id, ...installmentData };
    } catch (error) {
      console.error('Error updating installment:', error);
      throw error;
    }
  }

  async deleteInstallment(id) {
    try {
      await deleteDoc(doc(db, 'installments', id));
      console.log('✅ تم حذف القسط بنجاح');
      return true;
    } catch (error) {
      console.error('Error deleting installment:', error);
      throw error;
    }
  }

  // Authentication
  async login(pin) {
    // Simple PIN-based authentication
    const correctPIN = 'alihatem123';
    if (pin === correctPIN) {
      return { success: true, token: 'firebase_authenticated' };
    } else {
      throw new Error('رمز الدخول غير صحيح');
    }
  }
}

export default new FirebaseService();
