import React, { useState } from 'react';

const Settings = () => {
  const [theme, setTheme] = useState('light');
  const [fontSize, setFontSize] = useState('16px');
  const [pin, setPIN] = useState('');

  const applySettings = () => {
    document.documentElement.className = theme === 'dark' ? 'dark' : '';
    document.body.style.fontSize = fontSize;
    alert('✅ تم تطبيق الإعدادات');
  };

  const updatePIN = () => {
    if (pin.length < 4) return alert('❗ الرمز يجب أن يكون 4 خانات على الأقل');
    localStorage.setItem('pos_pin', pin);
    alert('🔐 تم تحديث رمز الدخول بنجاح');
  };

  return (
    <div className="p-6 max-w-2xl mx-auto min-h-screen">
      <div className="text-center mb-8 animate-fadeInUp">
        <h1 className="text-4xl font-bold text-gradient mb-2">⚙️ الإعدادات</h1>
        <p className="text-white text-opacity-80">تخصيص النظام والأمان</p>
      </div>

      <div className="space-y-8">
        {/* إعدادات المظهر */}
        <div className="glass p-8 rounded-3xl shadow-glow animate-slideInRight">
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
            <span className="text-3xl ml-3">🎨</span>
            إعدادات المظهر
          </h2>

          <div className="space-y-6">
            <div>
              <label className="block text-white font-medium mb-3">المظهر:</label>
              <select
                value={theme}
                onChange={(e) => setTheme(e.target.value)}
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white transition-all duration-300 focus:outline-none backdrop-blur-sm"
              >
                <option value="light" className="text-gray-800">فاتح</option>
                <option value="dark" className="text-gray-800">داكن</option>
              </select>
            </div>

            <div>
              <label className="block text-white font-medium mb-3">حجم الخط:</label>
              <select
                value={fontSize}
                onChange={(e) => setFontSize(e.target.value)}
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white transition-all duration-300 focus:outline-none backdrop-blur-sm"
              >
                <option value="14px" className="text-gray-800">خط صغير</option>
                <option value="16px" className="text-gray-800">خط متوسط</option>
                <option value="18px" className="text-gray-800">خط كبير</option>
              </select>
            </div>

            <button
              onClick={applySettings}
              className="btn-gradient w-full py-4 rounded-2xl text-white font-bold text-lg transform hover:scale-105 transition-all duration-300 shadow-glow-green"
            >
              <span className="flex items-center justify-center">
                <span className="text-xl ml-2">✅</span>
                تطبيق الإعدادات
              </span>
            </button>
          </div>
        </div>

        {/* إعدادات الأمان */}
        <div className="glass p-8 rounded-3xl shadow-glow animate-slideInRight" style={{ animationDelay: '0.2s' }}>
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
            <span className="text-3xl ml-3">🔐</span>
            إعدادات الأمان
          </h2>

          <div className="space-y-6">
            <div>
              <label className="block text-white font-medium mb-3">رمز دخول جديد:</label>
              <input
                type="password"
                placeholder="أدخل رمز دخول جديد"
                value={pin}
                onChange={(e) => setPIN(e.target.value)}
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
              />
              <p className="text-white text-opacity-60 text-sm mt-2">
                💡 يجب أن يكون الرمز 4 خانات على الأقل
              </p>
            </div>

            <button
              onClick={updatePIN}
              className="btn-gradient w-full py-4 rounded-2xl text-white font-bold text-lg transform hover:scale-105 transition-all duration-300 shadow-glow"
            >
              <span className="flex items-center justify-center">
                <span className="text-xl ml-2">🔐</span>
                حفظ رمز الدخول
              </span>
            </button>
          </div>
        </div>

        {/* معلومات النظام */}
        <div className="glass p-8 rounded-3xl shadow-glow animate-fadeInUp" style={{ animationDelay: '0.4s' }}>
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
            <span className="text-3xl ml-3">ℹ️</span>
            معلومات النظام
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-white">
            <div className="bg-white bg-opacity-10 p-4 rounded-2xl backdrop-blur-sm">
              <h3 className="font-bold mb-2 flex items-center">
                <span className="ml-2">📱</span>
                اسم النظام
              </h3>
              <p className="text-white text-opacity-80">نظام نقاط البيع</p>
            </div>

            <div className="bg-white bg-opacity-10 p-4 rounded-2xl backdrop-blur-sm">
              <h3 className="font-bold mb-2 flex items-center">
                <span className="ml-2">🔢</span>
                الإصدار
              </h3>
              <p className="text-white text-opacity-80">1.0.0</p>
            </div>

            <div className="bg-white bg-opacity-10 p-4 rounded-2xl backdrop-blur-sm">
              <h3 className="font-bold mb-2 flex items-center">
                <span className="ml-2">👨‍💻</span>
                المطور
              </h3>
              <p className="text-white text-opacity-80">Ali Hatem</p>
            </div>

            <div className="bg-white bg-opacity-10 p-4 rounded-2xl backdrop-blur-sm">
              <h3 className="font-bold mb-2 flex items-center">
                <span className="ml-2">📅</span>
                تاريخ الإنشاء
              </h3>
              <p className="text-white text-opacity-80">2024</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
