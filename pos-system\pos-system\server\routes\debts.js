import express from 'express';
import Debt from '../models/Debt.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Get all debts
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { status, customer } = req.query;
    let query = {};

    if (status && status !== 'all') {
      query.status = status;
    }

    if (customer) {
      query.customerName = { $regex: customer, $options: 'i' };
    }

    const debts = await Debt.find(query)
      .populate('relatedSale', 'total saleDate')
      .sort({ createdAt: -1 });

    res.json(debts);
  } catch (error) {
    console.error('Get debts error:', error);
    res.status(500).json({ message: 'خطأ في جلب الديون' });
  }
});

// Get debt by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const debt = await Debt.findById(req.params.id)
      .populate('relatedSale', 'total saleDate items');
    
    if (!debt) {
      return res.status(404).json({ message: 'الدين غير موجود' });
    }
    
    res.json(debt);
  } catch (error) {
    console.error('Get debt error:', error);
    res.status(500).json({ message: 'خطأ في جلب الدين' });
  }
});

// Create debt
router.post('/', authenticateToken, async (req, res) => {
  try {
    const debtData = {
      ...req.body,
      originalAmount: req.body.amount
    };
    
    const debt = new Debt(debtData);
    await debt.save();
    
    res.status(201).json(debt);
  } catch (error) {
    console.error('Create debt error:', error);
    res.status(400).json({ message: 'خطأ في إنشاء الدين', error: error.message });
  }
});

// Add payment to debt
router.post('/:id/payments', authenticateToken, async (req, res) => {
  try {
    const { amount, method = 'نقدي', notes } = req.body;
    
    if (!amount || amount <= 0) {
      return res.status(400).json({ message: 'مبلغ الدفع يجب أن يكون أكبر من صفر' });
    }

    const debt = await Debt.findById(req.params.id);
    if (!debt) {
      return res.status(404).json({ message: 'الدين غير موجود' });
    }

    if (amount > debt.amount) {
      return res.status(400).json({ message: 'مبلغ الدفع أكبر من المبلغ المستحق' });
    }

    debt.payments.push({
      amount,
      method,
      notes
    });

    await debt.save();
    res.json({ message: 'تم إضافة الدفعة بنجاح', debt });
  } catch (error) {
    console.error('Add payment error:', error);
    res.status(400).json({ message: 'خطأ في إضافة الدفعة', error: error.message });
  }
});

// Update debt
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const debt = await Debt.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!debt) {
      return res.status(404).json({ message: 'الدين غير موجود' });
    }
    
    res.json(debt);
  } catch (error) {
    console.error('Update debt error:', error);
    res.status(400).json({ message: 'خطأ في تحديث الدين', error: error.message });
  }
});

// Delete debt
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const debt = await Debt.findByIdAndDelete(req.params.id);
    
    if (!debt) {
      return res.status(404).json({ message: 'الدين غير موجود' });
    }
    
    res.json({ message: 'تم حذف الدين بنجاح' });
  } catch (error) {
    console.error('Delete debt error:', error);
    res.status(500).json({ message: 'خطأ في حذف الدين' });
  }
});

// Get debts statistics
router.get('/stats/summary', authenticateToken, async (req, res) => {
  try {
    const [totalDebts, paidDebts, pendingDebts, overdueDebts] = await Promise.all([
      Debt.aggregate([
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]),
      Debt.aggregate([
        { $match: { status: 'paid' } },
        { $group: { _id: null, total: { $sum: '$originalAmount' } } }
      ]),
      Debt.countDocuments({ status: 'pending' }),
      Debt.countDocuments({ status: 'overdue' })
    ]);

    const stats = {
      totalDebts: totalDebts[0]?.total || 0,
      paidDebts: paidDebts[0]?.total || 0,
      pendingDebts,
      overdueDebts,
      totalCustomers: await Debt.distinct('customerName').then(names => names.length)
    };

    res.json(stats);
  } catch (error) {
    console.error('Get debts stats error:', error);
    res.status(500).json({ message: 'خطأ في جلب إحصائيات الديون' });
  }
});

export default router;
