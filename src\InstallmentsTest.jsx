import React, { useState } from 'react';

const InstallmentsTest = () => {
  const [message, setMessage] = useState('منصة الأقساط تعمل!');
  const [installments, setInstallments] = useState([
    {
      id: 1,
      customerName: 'عميل تجريبي',
      totalAmount: 1000,
      installmentCount: 5,
      paidInstallments: 2,
      status: 'active'
    }
  ]);

  const addTestInstallment = () => {
    const newInstallment = {
      id: Date.now(),
      customerName: 'عميل جديد ' + Date.now(),
      totalAmount: 500,
      installmentCount: 3,
      paidInstallments: 0,
      status: 'active'
    };
    
    setInstallments([...installments, newInstallment]);
    setMessage('تم إضافة قسط جديد!');
  };

  const deleteInstallment = (id) => {
    setInstallments(installments.filter(i => i.id !== id));
    setMessage('تم حذف القسط!');
  };

  return (
    <div className="p-6 max-w-5xl mx-auto min-h-screen">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-white mb-2">🧪 اختبار منصة الأقساط</h1>
        <p className="text-white text-opacity-80">اختبار بسيط للتأكد من عمل المكون</p>
      </div>

      <div className="bg-green-500 bg-opacity-20 border border-green-500 text-green-300 p-4 rounded-lg mb-6 text-center">
        ✅ {message}
      </div>

      <div className="bg-white bg-opacity-10 p-6 rounded-3xl mb-8">
        <h2 className="text-2xl font-bold text-white mb-4">الإحصائيات</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-blue-500 bg-opacity-20 p-4 rounded-2xl text-center">
            <h3 className="text-white font-bold">إجمالي الأقساط</h3>
            <p className="text-2xl text-blue-300">{installments.length}</p>
          </div>
          <div className="bg-green-500 bg-opacity-20 p-4 rounded-2xl text-center">
            <h3 className="text-white font-bold">الأقساط النشطة</h3>
            <p className="text-2xl text-green-300">{installments.filter(i => i.status === 'active').length}</p>
          </div>
          <div className="bg-yellow-500 bg-opacity-20 p-4 rounded-2xl text-center">
            <h3 className="text-white font-bold">إجمالي المبالغ</h3>
            <p className="text-2xl text-yellow-300">{installments.reduce((sum, i) => sum + i.totalAmount, 0)} د.ع</p>
          </div>
        </div>
      </div>

      <div className="bg-white bg-opacity-10 p-6 rounded-3xl mb-8">
        <h2 className="text-2xl font-bold text-white mb-4">إضافة قسط تجريبي</h2>
        <button
          onClick={addTestInstallment}
          className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-2xl font-bold transition-all duration-300"
        >
          ➕ إضافة قسط تجريبي
        </button>
      </div>

      <div className="bg-white bg-opacity-10 p-6 rounded-3xl">
        <h2 className="text-2xl font-bold text-white mb-4">قائمة الأقساط</h2>
        
        {installments.length === 0 ? (
          <div className="text-center text-white text-opacity-60 py-8">
            <div className="text-4xl mb-2">💰</div>
            <p>لا توجد أقساط</p>
          </div>
        ) : (
          <div className="space-y-4">
            {installments.map((installment) => (
              <div key={installment.id} className="bg-white bg-opacity-10 p-4 rounded-2xl flex justify-between items-center">
                <div className="text-white">
                  <h3 className="font-bold text-lg">👤 {installment.customerName}</h3>
                  <p>💰 المبلغ: {installment.totalAmount} د.ع</p>
                  <p>📊 الأقساط: {installment.paidInstallments}/{installment.installmentCount}</p>
                  <p>📈 الحالة: {installment.status === 'active' ? '⏰ نشط' : '✅ مكتمل'}</p>
                </div>
                <button
                  onClick={() => deleteInstallment(installment.id)}
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-xl transition-all duration-300"
                >
                  🗑️ حذف
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="mt-8 bg-blue-500 bg-opacity-20 p-4 rounded-2xl">
        <h3 className="text-blue-300 font-bold mb-2">📋 معلومات التشخيص:</h3>
        <div className="text-blue-200 space-y-1">
          <p>🔧 React: يعمل</p>
          <p>🎨 CSS: يعمل</p>
          <p>📊 State: يعمل</p>
          <p>🖱️ Events: يعمل</p>
          <p>💾 localStorage: {localStorage ? 'متاح' : 'غير متاح'}</p>
          <p>🌐 المتصفح: {navigator.userAgent.includes('Chrome') ? 'Chrome' : 'متصفح آخر'}</p>
        </div>
      </div>

      <div className="text-center mt-8">
        <button
          onClick={() => {
            console.log('🧪 اختبار Console');
            console.log('📊 الأقساط الحالية:', installments);
            setMessage('تم طباعة البيانات في Console');
          }}
          className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-2xl font-bold transition-all duration-300"
        >
          🖥️ اختبار Console
        </button>
      </div>
    </div>
  );
};

export default InstallmentsTest;
