@echo off
echo ========================================
echo        رفع التطبيق إلى Netlify
echo ========================================

echo جاري بناء التطبيق...
call npm run build

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء التطبيق
    pause
    exit /b 1
)

echo ✅ تم بناء التطبيق بنجاح

echo.
echo جاري رفع التطبيق إلى Netlify...
echo.

:: تحقق من وجود Netlify CLI
netlify --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Netlify CLI غير مثبت
    echo يرجى تثبيته أولاً: npm install -g netlify-cli
    pause
    exit /b 1
)

:: تسجيل الدخول إلى Netlify
echo تسجيل الدخول إلى Netlify...
netlify login

:: رفع التطبيق
echo رفع التطبيق...
netlify deploy --prod --dir=dist

if %errorlevel% eq 0 (
    echo.
    echo ========================================
    echo ✅ تم رفع التطبيق بنجاح!
    echo 🌐 تحقق من الرابط في الرسالة أعلاه
    echo ========================================
) else (
    echo ❌ فشل في رفع التطبيق
)

pause
