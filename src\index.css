@tailwind base;
@tailwind components;
@tailwind utilities;

/* خطوط مخصصة وتحسينات عامة */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

* {
  font-family: 'Cairo', sans-serif;
}

body {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* تأثيرات الحركة */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* فئات مساعدة للحركة */
.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slideInRight {
  animation: slideInRight 0.5s ease-out;
}

.animate-pulse-custom {
  animation: pulse 2s infinite;
}

.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* تأثيرات الخلفية المتدرجة */
.gradient-bg-1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-2 {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-3 {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-bg-4 {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.gradient-bg-5 {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* تأثيرات الزجاج */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* تأثيرات الظلال المتقدمة */
.shadow-glow {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.4);
}

.shadow-glow-green {
  box-shadow: 0 0 20px rgba(67, 233, 123, 0.4);
}

.shadow-glow-red {
  box-shadow: 0 0 20px rgba(245, 87, 108, 0.4);
}

.shadow-glow-yellow {
  box-shadow: 0 0 20px rgba(254, 225, 64, 0.4);
}

/* تأثيرات الأزرار المتقدمة */
.btn-gradient {
  background: linear-gradient(45deg, #667eea, #764ba2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-gradient:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.btn-gradient:active {
  transform: translateY(0);
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-gradient:hover::before {
  left: 100%;
}

/* تأثيرات البطاقات */
.card-hover {
  transition: all 0.3s ease;
  position: relative;
}

.card-hover:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* تأثيرات الإدخال */
.input-glow:focus {
  box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
  border-color: #667eea;
  transform: scale(1.02);
}

/* تأثيرات النص المتحرك */
.text-gradient {
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}
