import express from 'express';
import Product from '../models/Product.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Get all products
router.get('/', authenticateToken, async (req, res) => {
  try {
    const { category, search, status } = req.query;
    let query = { isActive: true };

    if (category && category !== 'all') {
      query.category = category;
    }

    if (search) {
      query.name = { $regex: search, $options: 'i' };
    }

    const products = await Product.find(query).sort({ createdAt: -1 });
    
    let filteredProducts = products;
    if (status === 'low_stock') {
      filteredProducts = products.filter(p => p.stockStatus === 'low_stock');
    } else if (status === 'out_of_stock') {
      filteredProducts = products.filter(p => p.stockStatus === 'out_of_stock');
    }

    res.json(filteredProducts);
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json({ message: 'خطأ في جلب المنتجات' });
  }
});

// Get product by ID
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({ message: 'المنتج غير موجود' });
    }
    res.json(product);
  } catch (error) {
    console.error('Get product error:', error);
    res.status(500).json({ message: 'خطأ في جلب المنتج' });
  }
});

// Create product
router.post('/', authenticateToken, async (req, res) => {
  try {
    const product = new Product(req.body);
    await product.save();
    res.status(201).json(product);
  } catch (error) {
    console.error('Create product error:', error);
    res.status(400).json({ message: 'خطأ في إنشاء المنتج', error: error.message });
  }
});

// Update product
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const product = await Product.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!product) {
      return res.status(404).json({ message: 'المنتج غير موجود' });
    }
    
    res.json(product);
  } catch (error) {
    console.error('Update product error:', error);
    res.status(400).json({ message: 'خطأ في تحديث المنتج', error: error.message });
  }
});

// Delete product
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const product = await Product.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );
    
    if (!product) {
      return res.status(404).json({ message: 'المنتج غير موجود' });
    }
    
    res.json({ message: 'تم حذف المنتج بنجاح' });
  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json({ message: 'خطأ في حذف المنتج' });
  }
});

// Restock product
router.patch('/:id/restock', authenticateToken, async (req, res) => {
  try {
    const { quantity } = req.body;
    
    if (!quantity || quantity <= 0) {
      return res.status(400).json({ message: 'الكمية يجب أن تكون أكبر من صفر' });
    }

    const product = await Product.findById(req.params.id);
    if (!product) {
      return res.status(404).json({ message: 'المنتج غير موجود' });
    }

    product.quantity += parseInt(quantity);
    await product.save();

    res.json({ message: `تم إضافة ${quantity} قطعة للمخزون`, product });
  } catch (error) {
    console.error('Restock error:', error);
    res.status(500).json({ message: 'خطأ في إعادة التعبئة' });
  }
});

// Get inventory stats
router.get('/stats/inventory', authenticateToken, async (req, res) => {
  try {
    const products = await Product.find({ isActive: true });
    
    const stats = {
      totalProducts: products.length,
      totalValue: products.reduce((sum, p) => sum + p.totalValue, 0),
      totalProfit: products.reduce((sum, p) => sum + (p.profit * p.quantity), 0),
      lowStockCount: products.filter(p => p.stockStatus === 'low_stock').length,
      outOfStockCount: products.filter(p => p.stockStatus === 'out_of_stock').length,
      categories: {}
    };

    // Count by category
    products.forEach(product => {
      if (!stats.categories[product.category]) {
        stats.categories[product.category] = 0;
      }
      stats.categories[product.category]++;
    });

    res.json(stats);
  } catch (error) {
    console.error('Get inventory stats error:', error);
    res.status(500).json({ message: 'خطأ في جلب إحصائيات المخزون' });
  }
});

export default router;
