# نظام نقاط البيع - POS System

نظام نقاط بيع متكامل مع قاعدة بيانات MongoDB Atlas

## 🚀 البدء السريع

### 1. إعداد MongoDB Atlas

1. اذهب إلى [MongoDB Atlas](https://www.mongodb.com/atlas)
2. أنشئ حساب جديد أو سجل دخول
3. أنشئ Cluster جديد (اختر المجاني M0)
4. أنشئ Database User:
   - اذهب إلى Database Access
   - أضف مستخدم جديد مع كلمة مرور
5. أضف IP Address:
   - اذهب إلى Network Access
   - أضف IP الخاص بك أو 0.0.0.0/0 للوصول من أي مكان
6. احصل على Connection String:
   - اذهب إلى Clusters
   - اضغط Connect
   - اختر "Connect your application"
   - انسخ الـ connection string

### 2. إعداد Backend

```bash
# انتقل لمجلد الخادم
cd server

# تثبيت المكتبات
npm install

# إعداد متغيرات البيئة
# عدل ملف .env وأضف:
MONGODB_URI=mongodb+srv://username:<EMAIL>/pos_system?retryWrites=true&w=majority
JWT_SECRET=your_secret_key_here
PORT=5000
DEFAULT_PIN=alihatem123

# تشغيل الخادم
npm run dev
```

### 3. إعداد Frontend

```bash
# في المجلد الرئيسي
npm install

# تشغيل التطبيق
npm run dev
```

### 4. تشغيل النظام الكامل

```bash
# تشغيل Frontend و Backend معاً
npm run start:full
```

## 📁 هيكل المشروع

```
pos-system/
├── src/                    # Frontend (React)
│   ├── components/
│   ├── services/
│   │   └── api.js         # خدمة API
│   └── ...
├── server/                # Backend (Node.js)
│   ├── models/           # نماذج قاعدة البيانات
│   ├── routes/           # مسارات API
│   ├── middleware/       # Middleware
│   └── server.js         # ملف الخادم الرئيسي
└── README.md
```

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/login` - تسجيل الدخول
- `GET /api/auth/verify` - التحقق من الرمز

### Products
- `GET /api/products` - جلب المنتجات
- `POST /api/products` - إضافة منتج
- `PUT /api/products/:id` - تحديث منتج
- `DELETE /api/products/:id` - حذف منتج
- `PATCH /api/products/:id/restock` - إعادة تعبئة

### Sales
- `GET /api/sales` - جلب المبيعات
- `POST /api/sales` - إنشاء بيع
- `DELETE /api/sales/:id` - حذف بيع

### Debts
- `GET /api/debts` - جلب الديون
- `POST /api/debts` - إضافة دين
- `POST /api/debts/:id/payments` - إضافة دفعة

### Settings
- `GET /api/settings` - جلب الإعدادات
- `PUT /api/settings` - تحديث الإعدادات
- `PUT /api/settings/pin` - تحديث رمز الدخول

## 🔒 الأمان

- استخدام JWT للمصادقة
- تشفير كلمات المرور
- حماية جميع المسارات
- التحقق من صحة البيانات

## 🌟 المميزات

- ✅ إدارة المنتجات والمخزون
- ✅ نظام المبيعات المتكامل
- ✅ إدارة ديون العملاء
- ✅ تقارير مفصلة
- ✅ واجهة جميلة ومتجاوبة
- ✅ قاعدة بيانات سحابية
- ✅ نظام مصادقة آمن

## 🚨 ملاحظات مهمة

1. تأكد من تغيير `JWT_SECRET` في ملف .env
2. استخدم كلمة مرور قوية لقاعدة البيانات
3. لا تشارك ملف .env مع أحد
4. قم بعمل backup دوري لقاعدة البيانات

## 🆘 استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
- تأكد من صحة connection string
- تأكد من إضافة IP address في MongoDB Atlas
- تأكد من صحة اسم المستخدم وكلمة المرور

### خطأ في تشغيل الخادم
- تأكد من تثبيت جميع المكتبات
- تأكد من وجود ملف .env
- تأكد من أن المنفذ 5000 غير مستخدم

## 📞 الدعم

إذا واجهت أي مشاكل، تأكد من:
1. تثبيت Node.js (الإصدار 16 أو أحدث)
2. تثبيت جميع المكتبات المطلوبة
3. إعداد MongoDB Atlas بشكل صحيح
4. إعداد متغيرات البيئة في ملف .env
