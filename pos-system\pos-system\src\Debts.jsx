import React, { useState } from 'react';

const Debts = () => {
  const [debts, setDebts] = useState([
    { id: 1, customerName: 'أحمد محمد', amount: 25000, date: '2024-01-15' },
    { id: 2, customerName: 'فاطمة علي', amount: 15000, date: '2024-01-10' },
  ]);
  const [form, setForm] = useState({ customerName: '', amount: '', date: '' });

  const addDebt = () => {
    if (!form.customerName || !form.amount || !form.date) return;
    setDebts([...debts, { ...form, id: Date.now(), amount: parseFloat(form.amount) }]);
    setForm({ customerName: '', amount: '', date: '' });
  };

  const deleteDebt = (id) => {
    if (window.confirm('❗ هل تريد حذف هذا الدين؟')) {
      setDebts(debts.filter((d) => d.id !== id));
    }
  };

  const totalDebts = debts.reduce((sum, debt) => sum + debt.amount, 0);

  return (
    <div className="p-6 max-w-5xl mx-auto min-h-screen">
      <div className="text-center mb-8 animate-fadeInUp">
        <h1 className="text-4xl font-bold text-gradient mb-2">💳 إدارة الديون</h1>
        <p className="text-white text-opacity-80">متابعة ديون العملاء</p>
      </div>

      <div className="glass p-8 rounded-3xl shadow-glow-yellow mb-8 text-center animate-pulse-custom">
        <div className="text-5xl mb-4">📊</div>
        <h2 className="font-bold text-2xl text-white mb-2">إجمالي الديون</h2>
        <p className="text-4xl font-bold text-gradient">{totalDebts.toFixed(2)} دينار</p>
        <p className="text-white text-opacity-70 mt-2">{debts.length} عميل مدين</p>
      </div>

      <div className="glass p-8 rounded-3xl shadow-glow mb-8 animate-slideInRight">
        <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
          <span className="text-3xl ml-3">➕</span>
          إضافة دين جديد
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <input
            placeholder="اسم العميل"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.customerName}
            onChange={(e) => setForm({ ...form, customerName: e.target.value })}
          />
          <input
            placeholder="المبلغ"
            type="number"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.amount}
            onChange={(e) => setForm({ ...form, amount: e.target.value })}
          />
          <input
            type="date"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.date}
            onChange={(e) => setForm({ ...form, date: e.target.value })}
          />
        </div>

        <button
          onClick={addDebt}
          className="btn-gradient px-8 py-4 rounded-2xl text-white font-bold text-lg transform hover:scale-105 transition-all duration-300 shadow-glow"
        >
          <span className="flex items-center justify-center">
            <span className="text-xl ml-2">➕</span>
            إضافة دين
          </span>
        </button>
      </div>

      <div className="space-y-6">
        {debts.length === 0 ? (
          <div className="glass p-12 rounded-3xl text-center text-white text-opacity-60">
            <div className="text-6xl mb-4">💳</div>
            <p className="text-xl mb-2">لا توجد ديون مسجلة</p>
            <p className="text-sm opacity-80">ابدأ بإضافة ديون العملاء</p>
          </div>
        ) : (
          debts.map((debt, index) => (
            <div key={debt.id} className="glass p-6 rounded-3xl shadow-glow flex justify-between items-center card-hover animate-fadeInUp" style={{ animationDelay: `${index * 0.1}s` }}>
              <div className="text-white">
                <h3 className="font-bold text-xl mb-3 flex items-center">
                  <span className="text-2xl ml-2">👤</span>
                  {debt.customerName}
                </h3>
                <div className="space-y-1 text-white text-opacity-80">
                  <p className="flex items-center text-lg">
                    <span className="text-xl ml-2">💰</span>
                    المبلغ: <span className="font-bold text-red-300 mr-2">{debt.amount} دينار</span>
                  </p>
                  <p className="flex items-center">
                    <span className="text-lg ml-2">📅</span>
                    التاريخ: {debt.date}
                  </p>
                </div>
              </div>
              <button
                onClick={() => deleteDebt(debt.id)}
                className="bg-red-500 bg-opacity-80 hover:bg-opacity-100 text-white px-6 py-3 rounded-2xl font-medium transition-all duration-300 transform hover:scale-105"
              >
                <span className="flex items-center">
                  <span className="text-lg ml-1">🗑️</span>
                  حذف
                </span>
              </button>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default Debts;
