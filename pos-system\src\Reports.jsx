import React, { useState, useEffect } from 'react';
import apiService from './services/api';

const Reports = () => {
  const [sales, setSales] = useState([]);
  const [filter, setFilter] = useState('all');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadSales();
  }, []);

  const loadSales = async () => {
    try {
      setLoading(true);
      const data = await apiService.getSales();
      setSales(data);
    } catch (error) {
      console.error('Error loading sales:', error);
      // Fallback to localStorage if API fails
      const savedSales = JSON.parse(localStorage.getItem('sales') || '[]');
      setSales(savedSales);
    } finally {
      setLoading(false);
    }
  };

  const deleteSale = (id) => {
    if (window.confirm('❗ هل تريد حذف هذا البيع؟')) {
      const updatedSales = sales.filter((s) => s.id !== id);
      setSales(updatedSales);
      localStorage.setItem('sales', JSON.stringify(updatedSales));
    }
  };

  const getTodaysSales = () => {
    const today = new Date().toLocaleDateString('ar-EG');
    return sales.filter(sale => sale.date === today);
  };

  const getTotalSales = () => {
    return sales.reduce((sum, sale) => sum + sale.total, 0);
  };

  const getTodaysTotal = () => {
    return getTodaysSales().reduce((sum, sale) => sum + sale.total, 0);
  };

  const getFilteredSales = () => {
    if (filter === 'today') return getTodaysSales();
    return sales;
  };

  return (
    <div className="p-6 max-w-7xl mx-auto min-h-screen">
      <div className="text-center mb-8 animate-fadeInUp">
        <h1 className="text-4xl font-bold text-gradient mb-2">📊 التقارير والمبيعات</h1>
        <p className="text-white text-opacity-80">تحليل الأداء والمبيعات</p>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
        <div className="glass p-8 rounded-3xl text-center shadow-glow animate-fadeInUp card-hover">
          <div className="text-5xl mb-4">📈</div>
          <h3 className="text-xl font-bold text-white mb-3">إجمالي المبيعات</h3>
          <p className="text-4xl font-bold text-gradient mb-2">{getTotalSales().toFixed(2)} د.ع</p>
          <p className="text-sm text-white text-opacity-70">{sales.length} عملية بيع</p>
        </div>

        <div className="glass p-8 rounded-3xl text-center shadow-glow-green animate-fadeInUp card-hover" style={{ animationDelay: '0.1s' }}>
          <div className="text-5xl mb-4">📅</div>
          <h3 className="text-xl font-bold text-white mb-3">مبيعات اليوم</h3>
          <p className="text-4xl font-bold text-green-300 mb-2">{getTodaysTotal().toFixed(2)} د.ع</p>
          <p className="text-sm text-white text-opacity-70">{getTodaysSales().length} عملية بيع</p>
        </div>

        <div className="glass p-8 rounded-3xl text-center shadow-glow animate-fadeInUp card-hover" style={{ animationDelay: '0.2s' }}>
          <div className="text-5xl mb-4">💰</div>
          <h3 className="text-xl font-bold text-white mb-3">متوسط البيع</h3>
          <p className="text-4xl font-bold text-purple-300 mb-2">
            {sales.length > 0 ? (getTotalSales() / sales.length).toFixed(2) : '0.00'} د.ع
          </p>
          <p className="text-sm text-white text-opacity-70">لكل عملية بيع</p>
        </div>
      </div>

      {/* فلتر المبيعات */}
      <div className="glass p-6 rounded-3xl shadow-glow mb-8 animate-slideInRight">
        <div className="flex gap-6 items-center">
          <label className="font-bold text-white text-lg flex items-center">
            <span className="text-2xl ml-2">🔍</span>
            عرض:
          </label>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="input-glow border-0 p-3 rounded-2xl bg-white bg-opacity-20 text-white transition-all duration-300 focus:outline-none backdrop-blur-sm"
          >
            <option value="all" className="text-gray-800">جميع المبيعات</option>
            <option value="today" className="text-gray-800">مبيعات اليوم فقط</option>
          </select>
        </div>
      </div>

      {/* قائمة المبيعات */}
      <div className="glass rounded-3xl shadow-glow animate-fadeInUp">
        <div className="p-6 border-b border-white border-opacity-20">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <span className="text-3xl ml-3">🧾</span>
            سجل المبيعات
          </h2>
        </div>

        <div className="p-6">
          {getFilteredSales().length === 0 ? (
            <div className="text-center py-16 text-white text-opacity-60">
              <div className="text-6xl mb-4">📭</div>
              <p className="text-xl mb-2">لا توجد مبيعات بعد</p>
              <p className="text-sm opacity-80">ابدأ بإضافة مبيعات من صفحة نقطة البيع</p>
            </div>
          ) : (
            <div className="space-y-6">
              {getFilteredSales().reverse().map((sale, index) => (
                <div key={sale.id} className="bg-white bg-opacity-10 backdrop-blur-sm rounded-3xl p-6 hover:bg-opacity-20 transition-all duration-300 card-hover animate-slideInRight" style={{ animationDelay: `${index * 0.1}s` }}>
                  <div className="flex justify-between items-start mb-4">
                    <div className="text-white">
                      <h3 className="font-bold text-xl mb-2 flex items-center">
                        <span className="text-2xl ml-2">🧾</span>
                        فاتورة #{sale.id}
                      </h3>
                      <div className="space-y-1 text-sm text-white text-opacity-80">
                        <p className="flex items-center">
                          <span className="ml-2">📅</span>
                          {sale.date} - ⏰ {sale.time}
                        </p>
                        {sale.customer && (
                          <p className="flex items-center">
                            <span className="ml-2">👤</span>
                            العميل: {sale.customer}
                          </p>
                        )}
                        <p className="flex items-center">
                          <span className="ml-2">💳</span>
                          الدفع: {sale.paymentMethod}
                        </p>
                      </div>
                    </div>
                    <div className="text-left">
                      <p className="text-2xl font-bold text-green-300 mb-3">{sale.total.toFixed(2)} د.ع</p>
                      <button
                        onClick={() => deleteSale(sale.id)}
                        className="bg-red-500 bg-opacity-80 hover:bg-opacity-100 text-white px-4 py-2 rounded-2xl text-sm font-medium transition-all duration-300 transform hover:scale-105"
                      >
                        <span className="flex items-center">
                          <span className="ml-1">🗑️</span>
                          حذف
                        </span>
                      </button>
                    </div>
                  </div>

                  <div className="border-t border-white border-opacity-20 pt-4">
                    <p className="text-white font-medium mb-3 flex items-center">
                      <span className="text-lg ml-2">📦</span>
                      المنتجات:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {sale.items.map((item, itemIndex) => (
                        <div key={itemIndex} className="bg-white bg-opacity-10 rounded-2xl p-3 text-sm text-white backdrop-blur-sm">
                          <div className="font-medium">{item.name}</div>
                          <div className="text-white text-opacity-70">
                            {item.quantity} × {item.price} = {item.total} د.ع
                          </div>
                          {item.category && (
                            <div className="text-xs text-white text-opacity-60 bg-white bg-opacity-10 px-2 py-1 rounded-full inline-block mt-1">
                              {item.category}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Reports;
