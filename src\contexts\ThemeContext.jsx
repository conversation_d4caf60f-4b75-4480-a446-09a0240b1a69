import React, { createContext, useContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('light');
  const [fontSize, setFontSize] = useState('16px');

  // تحميل الإعدادات المحفوظة عند بدء التطبيق
  useEffect(() => {
    const savedTheme = localStorage.getItem('app_theme') || 'light';
    const savedFontSize = localStorage.getItem('app_fontSize') || '16px';
    
    setTheme(savedTheme);
    setFontSize(savedFontSize);
    
    // تطبيق الإعدادات فوراً
    applyTheme(savedTheme);
    applyFontSize(savedFontSize);
  }, []);

  const applyTheme = (newTheme) => {
    if (newTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  const applyFontSize = (newFontSize) => {
    document.body.style.fontSize = newFontSize;
  };

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    localStorage.setItem('app_theme', newTheme);
    applyTheme(newTheme);
  };

  const changeTheme = (newTheme) => {
    setTheme(newTheme);
    localStorage.setItem('app_theme', newTheme);
    applyTheme(newTheme);
  };

  const changeFontSize = (newFontSize) => {
    setFontSize(newFontSize);
    localStorage.setItem('app_fontSize', newFontSize);
    applyFontSize(newFontSize);
  };

  const value = {
    theme,
    fontSize,
    toggleTheme,
    changeTheme,
    changeFontSize,
    isDark: theme === 'dark'
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
