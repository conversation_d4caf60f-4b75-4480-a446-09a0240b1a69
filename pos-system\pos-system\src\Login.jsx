import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import apiService from './services/api';

const Login = () => {
  const [inputPIN, setInputPIN] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async () => {
    if (!inputPIN) {
      setError('❌ يرجى إدخال رمز الدخول');
      return;
    }

    try {
      setLoading(true);
      setError('');

      // التحقق من رمز الدخول محلياً أولاً
      const correctPIN = 'alihatem123';
      if (inputPIN !== correctPIN) {
        setError('❌ رمز الدخول غير صحيح');
        return;
      }

      try {
        // محاولة تسجيل الدخول عبر API
        const response = await apiService.login(inputPIN);
        console.log('تم تسجيل الدخول عبر الخادم');
      } catch (apiError) {
        // إنشاء token محلي إذا فشل API
        const localToken = 'local_token_' + Date.now();
        apiService.setToken(localToken);
        console.log('تم تسجيل الدخول محلياً');
      }

      // الانتقال لصفحة المبيعات في جميع الحالات
      navigate('/sales');

    } catch (error) {
      console.error('Login error:', error);
      setError('❌ ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center gradient-bg-1 relative overflow-hidden">
      {/* خلفية متحركة */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 bg-white rounded-full animate-pulse-custom"></div>
        <div className="absolute top-32 right-20 w-16 h-16 bg-white rounded-full animate-pulse-custom" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-20 left-32 w-12 h-12 bg-white rounded-full animate-pulse-custom" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-32 right-10 w-24 h-24 bg-white rounded-full animate-pulse-custom" style={{ animationDelay: '0.5s' }}></div>
      </div>

      <div className="glass p-8 rounded-3xl shadow-glow max-w-md w-full mx-4 animate-fadeInUp">
        <div className="text-center mb-8">
          <div className="text-6xl mb-4 animate-pulse-custom">🔐</div>
          <h1 className="text-3xl font-bold text-white mb-2">تسجيل الدخول</h1>
          <p className="text-white text-opacity-80">أدخل رمز الدخول للوصول للنظام</p>
        </div>

        <div className="space-y-4">
          <input
            type="password"
            className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            placeholder="أدخل رمز الدخول"
            value={inputPIN}
            onChange={(e) => setInputPIN(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleLogin()}
            autoComplete="off"
            maxLength="20"
          />

          {error && (
            <div className="bg-red-500 bg-opacity-20 border border-red-400 text-white p-3 rounded-xl animate-slideInRight">
              {error}
            </div>
          )}

          <button
            onClick={handleLogin}
            disabled={loading}
            className="btn-gradient w-full py-4 rounded-2xl text-white font-bold text-lg transform hover:scale-105 transition-all duration-300 shadow-glow disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <span className="flex items-center justify-center">
              {loading ? (
                <>
                  <span className="ml-2">⏳</span>
                  جاري التحقق...
                </>
              ) : (
                <>
                  <span className="ml-2">🚀</span>
                  دخول
                </>
              )}
            </span>
          </button>
        </div>


      </div>
    </div>
  );
};

export default Login;
