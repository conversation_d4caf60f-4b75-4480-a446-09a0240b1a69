@echo off
echo ========================================
echo        رفع التطبيق إلى Vercel
echo ========================================

echo جاري بناء التطبيق...
call npm run build

if %errorlevel% neq 0 (
    echo ❌ فشل في بناء التطبيق
    pause
    exit /b 1
)

echo ✅ تم بناء التطبيق بنجاح

echo.
echo جاري رفع التطبيق إلى Vercel...
echo.

:: تحقق من وجود Vercel CLI
vercel --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Vercel CLI غير مثبت
    echo يرجى تثبيته أولاً: npm install -g vercel
    pause
    exit /b 1
)

:: رفع التطبيق
echo رفع التطبيق...
vercel --prod

if %errorlevel% eq 0 (
    echo.
    echo ========================================
    echo ✅ تم رفع التطبيق بنجاح!
    echo 🌐 تحقق من الرابط في الرسالة أعلاه
    echo ========================================
) else (
    echo ❌ فشل في رفع التطبيق
)

pause
