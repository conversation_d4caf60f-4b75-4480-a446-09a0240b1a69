import React, { useState, useEffect } from 'react';
import { db } from './firebase/config';
import { collection, addDoc, getDocs, deleteDoc, doc } from 'firebase/firestore';

const FirebaseTest = () => {
  const [status, setStatus] = useState('testing');
  const [error, setError] = useState('');
  const [testData, setTestData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    testFirebaseConnection();
  }, []);

  const testFirebaseConnection = async () => {
    try {
      setLoading(true);
      setStatus('testing');
      setError('');
      
      console.log('🔄 اختبار اتصال Firebase...');
      
      // اختبار 1: قراءة البيانات
      console.log('📖 اختبار قراءة البيانات...');
      const querySnapshot = await getDocs(collection(db, 'test'));
      const data = [];
      querySnapshot.forEach((doc) => {
        data.push({ id: doc.id, ...doc.data() });
      });
      setTestData(data);
      console.log('✅ تم قراءة', data.length, 'عنصر');

      // اختبار 2: كتابة البيانات
      console.log('✍️ اختبار كتابة البيانات...');
      const testDoc = {
        message: 'اختبار Firebase',
        timestamp: new Date().toISOString(),
        testId: Date.now()
      };
      
      const docRef = await addDoc(collection(db, 'test'), testDoc);
      console.log('✅ تم إنشاء مستند بـ ID:', docRef.id);

      // اختبار 3: قراءة البيانات مرة أخرى
      console.log('🔄 إعادة قراءة البيانات...');
      const updatedSnapshot = await getDocs(collection(db, 'test'));
      const updatedData = [];
      updatedSnapshot.forEach((doc) => {
        updatedData.push({ id: doc.id, ...doc.data() });
      });
      setTestData(updatedData);
      console.log('✅ تم قراءة', updatedData.length, 'عنصر بعد الإضافة');

      setStatus('connected');
      console.log('🎉 Firebase يعمل بشكل مثالي!');
      
    } catch (error) {
      console.error('❌ خطأ في اختبار Firebase:', error);
      setError(error.message);
      setStatus('error');
    } finally {
      setLoading(false);
    }
  };

  const clearTestData = async () => {
    try {
      setLoading(true);
      console.log('🗑️ مسح بيانات الاختبار...');
      
      const querySnapshot = await getDocs(collection(db, 'test'));
      const deletePromises = [];
      
      querySnapshot.forEach((document) => {
        deletePromises.push(deleteDoc(doc(db, 'test', document.id)));
      });
      
      await Promise.all(deletePromises);
      setTestData([]);
      console.log('✅ تم مسح جميع بيانات الاختبار');
      
    } catch (error) {
      console.error('❌ خطأ في مسح البيانات:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'connected': return 'text-green-300 bg-green-500 bg-opacity-20';
      case 'testing': return 'text-yellow-300 bg-yellow-500 bg-opacity-20';
      case 'error': return 'text-red-300 bg-red-500 bg-opacity-20';
      default: return 'text-gray-300 bg-gray-500 bg-opacity-20';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'connected': return '✅';
      case 'testing': return '🔄';
      case 'error': return '❌';
      default: return '❓';
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connected': return 'Firebase متصل ويعمل';
      case 'testing': return 'جاري اختبار Firebase...';
      case 'error': return 'فشل في الاتصال بـ Firebase';
      default: return 'حالة غير معروفة';
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto min-h-screen">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-white mb-2">🔥 اختبار Firebase</h1>
        <p className="text-white text-opacity-80">فحص شامل لاتصال قاعدة البيانات السحابية</p>
      </div>

      {/* حالة الاتصال */}
      <div className="glass p-8 rounded-3xl mb-8">
        <div className="text-center mb-6">
          <div className={`inline-flex items-center px-6 py-4 rounded-2xl ${getStatusColor()}`}>
            <span className="text-3xl mr-3">{getStatusIcon()}</span>
            <div>
              <h3 className="font-bold text-lg">{getStatusText()}</h3>
              {loading && <p className="text-sm opacity-80">جاري المعالجة...</p>}
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-500 bg-opacity-20 border border-red-500 text-red-300 p-4 rounded-lg mb-6">
            <h4 className="font-bold mb-2">❌ تفاصيل الخطأ:</h4>
            <p className="text-sm font-mono">{error}</p>
          </div>
        )}

        <div className="flex gap-4 justify-center">
          <button
            onClick={testFirebaseConnection}
            disabled={loading}
            className="bg-blue-500 bg-opacity-80 hover:bg-opacity-100 text-white px-6 py-3 rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <span className="flex items-center">
              <span className="text-lg mr-2">🔄</span>
              إعادة الاختبار
            </span>
          </button>

          <button
            onClick={clearTestData}
            disabled={loading || testData.length === 0}
            className="bg-red-500 bg-opacity-80 hover:bg-opacity-100 text-white px-6 py-3 rounded-2xl font-bold transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            <span className="flex items-center">
              <span className="text-lg mr-2">🗑️</span>
              مسح بيانات الاختبار
            </span>
          </button>
        </div>
      </div>

      {/* معلومات Firebase */}
      <div className="glass p-6 rounded-3xl mb-8">
        <h3 className="text-xl font-bold text-white mb-4">🔧 معلومات Firebase</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-white text-opacity-80">
          <div>
            <p className="font-medium">🆔 Project ID</p>
            <p className="text-blue-300 font-mono text-sm">pos-system-bfc74</p>
          </div>
          <div>
            <p className="font-medium">🌐 Auth Domain</p>
            <p className="text-blue-300 font-mono text-sm">pos-system-bfc74.firebaseapp.com</p>
          </div>
          <div>
            <p className="font-medium">💾 Database</p>
            <p className="text-green-300">Firestore</p>
          </div>
          <div>
            <p className="font-medium">📊 بيانات الاختبار</p>
            <p className="text-yellow-300">{testData.length} عنصر</p>
          </div>
        </div>
      </div>

      {/* بيانات الاختبار */}
      {testData.length > 0 && (
        <div className="glass p-6 rounded-3xl">
          <h3 className="text-xl font-bold text-white mb-4">📊 بيانات الاختبار</h3>
          <div className="space-y-3">
            {testData.map((item, index) => (
              <div key={item.id} className="bg-white bg-opacity-10 p-4 rounded-2xl">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-white font-medium">#{index + 1} {item.message}</p>
                    <p className="text-white text-opacity-60 text-sm">
                      🕒 {new Date(item.timestamp).toLocaleString('ar-SA')}
                    </p>
                  </div>
                  <div className="text-white text-opacity-60 text-xs font-mono">
                    ID: {item.id}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* تعليمات الاستكشاف */}
      <div className="glass p-6 rounded-3xl mt-8">
        <h3 className="text-xl font-bold text-white mb-4">🔍 استكشاف الأخطاء</h3>
        <div className="text-white text-opacity-80 space-y-3">
          <div className="flex items-start">
            <span className="text-lg mr-3">1️⃣</span>
            <div>
              <p className="font-medium">تحقق من الإنترنت</p>
              <p className="text-sm opacity-80">تأكد من وجود اتصال إنترنت مستقر</p>
            </div>
          </div>
          
          <div className="flex items-start">
            <span className="text-lg mr-3">2️⃣</span>
            <div>
              <p className="font-medium">تحقق من Console</p>
              <p className="text-sm opacity-80">اضغط F12 وابحث عن رسائل الخطأ في تبويب Console</p>
            </div>
          </div>
          
          <div className="flex items-start">
            <span className="text-lg mr-3">3️⃣</span>
            <div>
              <p className="font-medium">تحقق من إعدادات Firebase</p>
              <p className="text-sm opacity-80">تأكد من صحة معرف المشروع وإعدادات الأمان</p>
            </div>
          </div>
          
          <div className="flex items-start">
            <span className="text-lg mr-3">4️⃣</span>
            <div>
              <p className="font-medium">تحقق من قواعد Firestore</p>
              <p className="text-sm opacity-80">تأكد من أن قواعد قاعدة البيانات تسمح بالقراءة والكتابة</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FirebaseTest;
