const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

class ApiService {
  constructor() {
    this.token = localStorage.getItem('auth_token');
  }

  setToken(token) {
    this.token = token;
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  getHeaders() {
    const headers = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    return headers;
  }

  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: this.getHeaders(),
      ...options,
    };

    console.log(`🔗 محاولة الاتصال بـ: ${url}`);

    try {
      const response = await fetch(url, config);

      console.log(`📡 استجابة الخادم: ${response.status}`);

      if (!response.ok) {
        const data = await response.json().catch(() => ({}));
        console.error(`❌ خطأ من الخادم: ${response.status}`, data);
        throw new Error(data.message || `خطأ HTTP: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ تم الحصول على البيانات من الخادم');
      return data;
    } catch (error) {
      console.error('❌ فشل الاتصال بالخادم:', error.message);

      // إذا كان خطأ شبكة أو اتصال
      if (error.name === 'TypeError' || error.message.includes('fetch')) {
        console.log('🔄 سيتم استخدام التخزين المحلي');
        throw new Error('OFFLINE');
      }

      throw error;
    }
  }

  // Auth methods
  async login(pin) {
    try {
      const data = await this.request('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ pin }),
      });

      if (data.token) {
        this.setToken(data.token);
        console.log('✅ تم تسجيل الدخول بنجاح');
      }

      return data;
    } catch (error) {
      console.error('Login failed:', error);
      // إنشاء token وهمي للعمل المحلي
      const fakeToken = 'local_token_' + Date.now();
      this.setToken(fakeToken);
      throw error;
    }
  }

  async verifyToken() {
    return this.request('/auth/verify');
  }

  logout() {
    this.setToken(null);
  }

  // Products methods
  async getProducts(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request(`/products${queryString ? `?${queryString}` : ''}`);
  }

  async getProduct(id) {
    return this.request(`/products/${id}`);
  }

  async createProduct(productData) {
    return this.request('/products', {
      method: 'POST',
      body: JSON.stringify(productData),
    });
  }

  async updateProduct(id, productData) {
    return this.request(`/products/${id}`, {
      method: 'PUT',
      body: JSON.stringify(productData),
    });
  }

  async deleteProduct(id) {
    return this.request(`/products/${id}`, {
      method: 'DELETE',
    });
  }

  async restockProduct(id, quantity) {
    return this.request(`/products/${id}/restock`, {
      method: 'PATCH',
      body: JSON.stringify({ quantity }),
    });
  }

  async getInventoryStats() {
    return this.request('/products/stats/inventory');
  }

  // Sales methods
  async getSales(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request(`/sales${queryString ? `?${queryString}` : ''}`);
  }

  async getSale(id) {
    return this.request(`/sales/${id}`);
  }

  async createSale(saleData) {
    return this.request('/sales', {
      method: 'POST',
      body: JSON.stringify(saleData),
    });
  }

  async cancelSale(id) {
    return this.request(`/sales/${id}/cancel`, {
      method: 'PATCH',
    });
  }

  async deleteSale(id) {
    return this.request(`/sales/${id}`, {
      method: 'DELETE',
    });
  }

  async getSalesStats() {
    return this.request('/sales/stats/summary');
  }

  // Debts methods
  async getDebts(params = {}) {
    const queryString = new URLSearchParams(params).toString();
    return this.request(`/debts${queryString ? `?${queryString}` : ''}`);
  }

  async getDebt(id) {
    return this.request(`/debts/${id}`);
  }

  async createDebt(debtData) {
    return this.request('/debts', {
      method: 'POST',
      body: JSON.stringify(debtData),
    });
  }

  async addPayment(debtId, paymentData) {
    return this.request(`/debts/${debtId}/payments`, {
      method: 'POST',
      body: JSON.stringify(paymentData),
    });
  }

  async updateDebt(id, debtData) {
    return this.request(`/debts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(debtData),
    });
  }

  async deleteDebt(id) {
    return this.request(`/debts/${id}`, {
      method: 'DELETE',
    });
  }

  async getDebtsStats() {
    return this.request('/debts/stats/summary');
  }

  // Settings methods
  async getSettings() {
    return this.request('/settings');
  }

  async updateSettings(settingsData) {
    return this.request('/settings', {
      method: 'PUT',
      body: JSON.stringify(settingsData),
    });
  }

  async updatePIN(currentPin, newPin) {
    return this.request('/settings/pin', {
      method: 'PUT',
      body: JSON.stringify({ currentPin, newPin }),
    });
  }

  async resetSettings() {
    return this.request('/settings/reset', {
      method: 'POST',
    });
  }
}

export default new ApiService();
