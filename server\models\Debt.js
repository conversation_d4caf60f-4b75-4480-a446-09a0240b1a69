import mongoose from 'mongoose';

const debtSchema = new mongoose.Schema({
  customerName: {
    type: String,
    required: true,
    trim: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  originalAmount: {
    type: Number,
    required: true,
    min: 0
  },
  debtDate: {
    type: Date,
    required: true
  },
  dueDate: {
    type: Date
  },
  status: {
    type: String,
    enum: ['pending', 'partial', 'paid', 'overdue'],
    default: 'pending'
  },
  payments: [{
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    paymentDate: {
      type: Date,
      default: Date.now
    },
    method: {
      type: String,
      enum: ['نقدي', 'بطاقة', 'تحويل'],
      default: 'نقدي'
    },
    notes: String
  }],
  notes: String,
  customerPhone: String,
  customerAddress: String,
  relatedSale: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Sale'
  }
}, {
  timestamps: true
});

// Calculate remaining amount
debtSchema.virtual('remainingAmount').get(function() {
  const totalPaid = this.payments.reduce((sum, payment) => sum + payment.amount, 0);
  return this.originalAmount - totalPaid;
});

// Update status based on payments
debtSchema.pre('save', function(next) {
  const totalPaid = this.payments.reduce((sum, payment) => sum + payment.amount, 0);
  this.amount = this.originalAmount - totalPaid;
  
  if (this.amount <= 0) {
    this.status = 'paid';
  } else if (totalPaid > 0) {
    this.status = 'partial';
  } else if (this.dueDate && new Date() > this.dueDate) {
    this.status = 'overdue';
  } else {
    this.status = 'pending';
  }
  
  next();
});

debtSchema.set('toJSON', { virtuals: true });

export default mongoose.model('Debt', debtSchema);
