import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Navigation from './Navigation';
import Login from './Login';
import Sales from './Sales';
import Inventory from './Inventory';
import Reports from './Reports';
import Debts from './Debts';
import DebtsTest from './DebtsTest';
import DebtsSimple from './DebtsSimple';
import Settings from './Settings';
import apiService from './services/api';

const App = () => {
  useEffect(() => {
    const currentPIN = localStorage.getItem('pos_pin');
    if (!currentPIN) {
      localStorage.setItem('pos_pin', 'alihatem123');
    }

    // إعداد token افتراضي إذا لم يكن موجود
    if (!apiService.token) {
      const existingToken = localStorage.getItem('auth_token');
      if (existingToken) {
        apiService.setToken(existingToken);
      } else {
        // إنشاء token محلي افتراضي
        const defaultToken = 'local_token_default';
        apiService.setToken(defaultToken);
      }
    }
  }, []);

  return (
    <Router>
      <div className="min-h-screen gradient-bg-1 relative overflow-hidden">
        {/* خلفية متحركة */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-32 h-32 bg-white rounded-full animate-pulse-custom"></div>
          <div className="absolute top-40 right-32 w-24 h-24 bg-white rounded-full animate-pulse-custom" style={{ animationDelay: '1s' }}></div>
          <div className="absolute bottom-32 left-40 w-20 h-20 bg-white rounded-full animate-pulse-custom" style={{ animationDelay: '2s' }}></div>
          <div className="absolute bottom-20 right-20 w-36 h-36 bg-white rounded-full animate-pulse-custom" style={{ animationDelay: '0.5s' }}></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-white rounded-full animate-pulse-custom" style={{ animationDelay: '1.5s' }}></div>
        </div>

        <div className="relative z-10">
          <Navigation />
          <Routes>
            <Route path="/" element={<Navigate to="/login" />} />
            <Route path="/login" element={<Login />} />
            <Route path="/sales" element={<Sales />} />
            <Route path="/inventory" element={<Inventory />} />
            <Route path="/reports" element={<Reports />} />
            <Route path="/debts" element={<DebtsSimple />} />
            <Route path="/debts-test" element={<DebtsTest />} />
            <Route path="/debts-firebase" element={<Debts />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </div>
      </div>
    </Router>
  );
};

export default App;
