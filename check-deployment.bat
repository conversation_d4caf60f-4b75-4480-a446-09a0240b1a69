@echo off
echo ========================================
echo      فحص جاهزية التطبيق للنشر
echo ========================================

echo.
echo 🔍 فحص الملفات الأساسية...

:: فحص package.json
if exist "package.json" (
    echo ✅ package.json موجود
) else (
    echo ❌ package.json مفقود
    goto :error
)

:: فحص firebase.json
if exist "firebase.json" (
    echo ✅ firebase.json موجود
) else (
    echo ❌ firebase.json مفقود
    goto :error
)

:: فحص vercel.json
if exist "vercel.json" (
    echo ✅ vercel.json موجود
) else (
    echo ⚠️ vercel.json مفقود (اختياري)
)

:: فحص src
if exist "src" (
    echo ✅ مجلد src موجود
) else (
    echo ❌ مجلد src مفقود
    goto :error
)

echo.
echo 🔧 فحص التبعيات...

:: فحص node_modules
if exist "node_modules" (
    echo ✅ node_modules موجود
) else (
    echo ❌ node_modules مفقود - تشغيل npm install...
    call npm install
)

echo.
echo 🏗️ اختبار البناء...

call npm run build
if %errorlevel% eq 0 (
    echo ✅ البناء نجح
) else (
    echo ❌ فشل البناء
    goto :error
)

:: فحص مجلد dist
if exist "dist" (
    echo ✅ مجلد dist تم إنشاؤه
) else (
    echo ❌ مجلد dist مفقود
    goto :error
)

:: فحص index.html في dist
if exist "dist\index.html" (
    echo ✅ dist\index.html موجود
) else (
    echo ❌ dist\index.html مفقود
    goto :error
)

echo.
echo 🌐 فحص إعدادات Firebase...

:: فحص Firebase config
if exist "src\firebase\config.js" (
    echo ✅ Firebase config موجود
) else (
    echo ❌ Firebase config مفقود
    goto :error
)

echo.
echo ========================================
echo ✅ التطبيق جاهز للنشر!
echo ========================================
echo.
echo الخطوات التالية:
echo 1. للنشر على Firebase: .\deploy-firebase.bat
echo 2. للنشر على Vercel: .\deploy-vercel.bat
echo 3. للنشر على Netlify: .\deploy-netlify.bat
echo.
goto :end

:error
echo.
echo ========================================
echo ❌ التطبيق غير جاهز للنشر!
echo ========================================
echo يرجى إصلاح الأخطاء أعلاه أولاً
echo.

:end
pause
