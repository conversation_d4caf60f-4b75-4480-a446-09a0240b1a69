import React, { useState, useEffect } from 'react';
import firebaseService from './services/firebaseService';

const Installments = () => {
  const [installments, setInstallments] = useState([]);
  const [form, setForm] = useState({
    customerName: '',
    customerPhone: '',
    productName: '',
    totalAmount: '',
    downPayment: '',
    installmentAmount: '',
    installmentCount: '',
    startDate: '',
    notes: ''
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedInstallment, setSelectedInstallment] = useState(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  useEffect(() => {
    loadInstallments();
  }, []);

  const loadInstallments = async () => {
    try {
      setLoading(true);
      setError('');

      const installmentsData = await firebaseService.getInstallments();
      setInstallments(installmentsData || []);
      localStorage.setItem('local_installments', JSON.stringify(installmentsData || []));
    } catch (error) {
      console.error('خطأ في تحميل الأقساط:', error);
      setError('فشل في تحميل الأقساط من Firebase: ' + error.message);

      const localInstallments = localStorage.getItem('local_installments');
      if (localInstallments) {
        setInstallments(JSON.parse(localInstallments));
      }
    } finally {
      setLoading(false);
    }
  };

  const calculateInstallmentDetails = () => {
    const total = parseFloat(form.totalAmount) || 0;
    const down = parseFloat(form.downPayment) || 0;
    const count = parseInt(form.installmentCount) || 1;
    
    const remainingAmount = total - down;
    const monthlyAmount = remainingAmount / count;
    
    return {
      remainingAmount,
      monthlyAmount: monthlyAmount.toFixed(2)
    };
  };

  const addInstallment = async () => {
    if (!form.customerName || !form.totalAmount || !form.installmentCount || !form.startDate) {
      setError('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    try {
      setError('');
      const { remainingAmount, monthlyAmount } = calculateInstallmentDetails();

      const installmentData = {
        customerName: form.customerName.trim(),
        customerPhone: form.customerPhone.trim(),
        productName: form.productName.trim(),
        totalAmount: parseFloat(form.totalAmount),
        downPayment: parseFloat(form.downPayment) || 0,
        remainingAmount: parseFloat(remainingAmount),
        installmentAmount: parseFloat(monthlyAmount),
        installmentCount: parseInt(form.installmentCount),
        paidInstallments: 0,
        startDate: new Date(form.startDate),
        notes: form.notes?.trim() || '',
        status: 'active',
        payments: []
      };

      const newInstallment = await firebaseService.createInstallment(installmentData);
      setInstallments(prevInstallments => [newInstallment, ...prevInstallments]);

      setForm({
        customerName: '',
        customerPhone: '',
        productName: '',
        totalAmount: '',
        downPayment: '',
        installmentAmount: '',
        installmentCount: '',
        startDate: '',
        notes: ''
      });

      const updatedInstallments = [newInstallment, ...installments];
      localStorage.setItem('local_installments', JSON.stringify(updatedInstallments));

    } catch (error) {
      console.error('خطأ في إضافة القسط:', error);
      setError('فشل في إضافة القسط: ' + error.message);
    }
  };

  const deleteInstallment = async (id) => {
    if (!window.confirm('❗ هل تريد حذف هذا القسط؟')) return;

    try {
      setError('');
      await firebaseService.deleteInstallment(id);

      const updatedInstallments = installments.filter((i) => i.id !== id);
      setInstallments(updatedInstallments);
      localStorage.setItem('local_installments', JSON.stringify(updatedInstallments));

    } catch (error) {
      console.error('خطأ في حذف القسط:', error);
      setError('فشل في حذف القسط: ' + error.message);
    }
  };

  const addPayment = async (installmentId, paymentAmount) => {
    try {
      const installment = installments.find(i => i.id === installmentId);
      if (!installment) return;

      const payment = {
        amount: parseFloat(paymentAmount),
        date: new Date(),
        id: Date.now()
      };

      const updatedPayments = [...(installment.payments || []), payment];
      const paidInstallments = installment.paidInstallments + 1;
      const status = paidInstallments >= installment.installmentCount ? 'completed' : 'active';

      const updatedInstallmentData = {
        ...installment,
        payments: updatedPayments,
        paidInstallments,
        status
      };

      await firebaseService.updateInstallment(installmentId, updatedInstallmentData);

      const updatedInstallments = installments.map(i =>
        i.id === installmentId ? updatedInstallmentData : i
      );

      setInstallments(updatedInstallments);
      localStorage.setItem('local_installments', JSON.stringify(updatedInstallments));
      setShowPaymentModal(false);
      setSelectedInstallment(null);

    } catch (error) {
      console.error('خطأ في إضافة الدفعة:', error);
      setError('فشل في إضافة الدفعة: ' + error.message);
    }
  };

  const getNextPaymentDate = (installment) => {
    if (!installment.startDate) return 'غير محدد';
    
    const startDate = new Date(installment.startDate);
    const nextMonth = installment.paidInstallments + 1;
    const nextDate = new Date(startDate);
    nextDate.setMonth(startDate.getMonth() + nextMonth);
    
    return nextDate.toLocaleDateString('ar-SA');
  };

  const isOverdue = (installment) => {
    if (installment.status === 'completed') return false;
    
    const startDate = new Date(installment.startDate);
    const expectedPaymentDate = new Date(startDate);
    expectedPaymentDate.setMonth(startDate.getMonth() + installment.paidInstallments + 1);
    
    return new Date() > expectedPaymentDate;
  };

  const totalInstallments = installments.length;
  const activeInstallments = installments.filter(i => i.status === 'active').length;
  const completedInstallments = installments.filter(i => i.status === 'completed').length;
  const overdueInstallments = installments.filter(i => isOverdue(i)).length;
  const totalRevenue = installments.reduce((sum, i) => sum + (i.totalAmount || 0), 0);

  if (loading) {
    return (
      <div className="p-6 max-w-7xl mx-auto min-h-screen flex items-center justify-center">
        <div className="glass p-12 rounded-3xl text-center">
          <div className="text-6xl mb-4 animate-spin">⏳</div>
          <p className="text-white text-xl">جاري تحميل الأقساط...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto min-h-screen">
      <div className="text-center mb-8 animate-fadeInUp">
        <h1 className="text-4xl font-bold text-gradient mb-2">💳 منصة الأقساط</h1>
        <p className="text-white text-opacity-80">إدارة شاملة لنظام الأقساط والدفعات</p>
      </div>

      {error && (
        <div className="glass p-4 rounded-2xl mb-6 bg-red-500 bg-opacity-20 border border-red-500 border-opacity-30">
          <p className="text-red-300 text-center">❌ {error}</p>
          <button 
            onClick={() => setError('')}
            className="mt-2 text-red-200 hover:text-white transition-colors duration-200 block mx-auto"
          >
            إغلاق
          </button>
        </div>
      )}

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <div className="glass p-6 rounded-3xl text-center animate-pulse-custom">
          <div className="text-4xl mb-2">📊</div>
          <h3 className="font-bold text-lg text-white mb-1">إجمالي الأقساط</h3>
          <p className="text-2xl font-bold text-blue-300">{totalInstallments}</p>
        </div>
        
        <div className="glass p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">⏰</div>
          <h3 className="font-bold text-lg text-white mb-1">نشطة</h3>
          <p className="text-2xl font-bold text-green-300">{activeInstallments}</p>
        </div>
        
        <div className="glass p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">✅</div>
          <h3 className="font-bold text-lg text-white mb-1">مكتملة</h3>
          <p className="text-2xl font-bold text-blue-300">{completedInstallments}</p>
        </div>
        
        <div className="glass p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">🚨</div>
          <h3 className="font-bold text-lg text-white mb-1">متأخرة</h3>
          <p className="text-2xl font-bold text-red-300">{overdueInstallments}</p>
        </div>
        
        <div className="glass p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">💰</div>
          <h3 className="font-bold text-lg text-white mb-1">إجمالي الإيرادات</h3>
          <p className="text-xl font-bold text-yellow-300">{totalRevenue.toFixed(2)} د.ع</p>
        </div>
      </div>

      {/* نموذج إضافة قسط جديد */}
      <div className="glass p-8 rounded-3xl shadow-glow mb-8 animate-slideInRight">
        <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
          <span className="text-3xl ml-3">➕</span>
          إضافة قسط جديد
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <input
            placeholder="اسم العميل *"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.customerName}
            onChange={(e) => setForm({ ...form, customerName: e.target.value })}
            required
          />
          
          <input
            placeholder="رقم الهاتف"
            type="tel"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.customerPhone}
            onChange={(e) => setForm({ ...form, customerPhone: e.target.value })}
          />
          
          <input
            placeholder="اسم المنتج"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.productName}
            onChange={(e) => setForm({ ...form, productName: e.target.value })}
          />
          
          <input
            placeholder="المبلغ الإجمالي *"
            type="number"
            step="0.01"
            min="0"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.totalAmount}
            onChange={(e) => setForm({ ...form, totalAmount: e.target.value })}
            required
          />
          
          <input
            placeholder="الدفعة المقدمة"
            type="number"
            step="0.01"
            min="0"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.downPayment}
            onChange={(e) => setForm({ ...form, downPayment: e.target.value })}
          />
          
          <input
            placeholder="عدد الأقساط *"
            type="number"
            min="1"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.installmentCount}
            onChange={(e) => setForm({ ...form, installmentCount: e.target.value })}
            required
          />
          
          <input
            type="date"
            title="تاريخ بداية الأقساط"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white transition-all duration-300 focus:outline-none backdrop-blur-sm"
            value={form.startDate}
            onChange={(e) => setForm({ ...form, startDate: e.target.value })}
            required
          />
          
          <textarea
            placeholder="ملاحظات"
            className="input-glow border-0 p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm resize-none md:col-span-2"
            rows="1"
            value={form.notes}
            onChange={(e) => setForm({ ...form, notes: e.target.value })}
          />
        </div>

        {/* معاينة حساب القسط */}
        {form.totalAmount && form.installmentCount && (
          <div className="bg-blue-500 bg-opacity-20 p-4 rounded-2xl mb-6 border border-blue-500 border-opacity-30">
            <h3 className="text-blue-300 font-bold mb-2">📊 معاينة الحساب:</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-blue-200">
              <p>💰 المبلغ المتبقي: {calculateInstallmentDetails().remainingAmount.toFixed(2)} د.ع</p>
              <p>📅 قسط شهري: {calculateInstallmentDetails().monthlyAmount} د.ع</p>
              <p>🔢 عدد الأقساط: {form.installmentCount} قسط</p>
            </div>
          </div>
        )}

        <button
          onClick={addInstallment}
          disabled={!form.customerName || !form.totalAmount || !form.installmentCount || !form.startDate}
          className="btn-gradient px-8 py-4 rounded-2xl text-white font-bold text-lg transform hover:scale-105 transition-all duration-300 shadow-glow disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          <span className="flex items-center justify-center">
            <span className="text-xl ml-2">➕</span>
            إضافة قسط
          </span>
        </button>
      </div>

      {/* قائمة الأقساط */}
      <div className="space-y-6">
        {installments.length === 0 ? (
          <div className="glass p-12 rounded-3xl text-center text-white text-opacity-60">
            <div className="text-6xl mb-4">💳</div>
            <p className="text-xl mb-2">لا توجد أقساط مسجلة</p>
            <p className="text-sm opacity-80">ابدأ بإضافة قسط جديد</p>
          </div>
        ) : (
          installments.map((installment, index) => {
            const overdue = isOverdue(installment);
            const progress = (installment.paidInstallments / installment.installmentCount) * 100;
            
            return (
              <div 
                key={installment.id} 
                className={`glass p-6 rounded-3xl shadow-glow card-hover animate-fadeInUp ${overdue ? 'border-2 border-red-500 border-opacity-50' : ''}`} 
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-bold text-xl text-white flex items-center">
                        <span className="text-2xl ml-2">👤</span>
                        {installment.customerName}
                      </h3>
                      
                      <div className="flex items-center space-x-2 space-x-reverse">
                        {overdue && (
                          <span className="px-3 py-1 bg-red-500 bg-opacity-30 text-red-300 text-sm rounded-full">
                            🚨 متأخر
                          </span>
                        )}
                        <span className={`px-3 py-1 text-sm rounded-full ${
                          installment.status === 'completed' ? 'bg-green-500 bg-opacity-30 text-green-300' :
                          'bg-blue-500 bg-opacity-30 text-blue-300'
                        }`}>
                          {installment.status === 'completed' ? '✅ مكتمل' : '⏰ نشط'}
                        </span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-white text-opacity-80 mb-4">
                      <div className="space-y-2">
                        <p className="flex items-center">
                          <span className="text-lg ml-2">💰</span>
                          المبلغ الإجمالي: {installment.totalAmount?.toFixed(2)} د.ع
                        </p>
                        <p className="flex items-center">
                          <span className="text-lg ml-2">💵</span>
                          الدفعة المقدمة: {installment.downPayment?.toFixed(2)} د.ع
                        </p>
                        <p className="flex items-center">
                          <span className="text-lg ml-2">📅</span>
                          قسط شهري: {installment.installmentAmount?.toFixed(2)} د.ع
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <p className="flex items-center">
                          <span className="text-lg ml-2">🔢</span>
                          الأقساط: {installment.paidInstallments}/{installment.installmentCount}
                        </p>
                        <p className="flex items-center">
                          <span className="text-lg ml-2">📅</span>
                          تاريخ البداية: {new Date(installment.startDate).toLocaleDateString('ar-SA')}
                        </p>
                        <p className="flex items-center">
                          <span className="text-lg ml-2">⏰</span>
                          القسط التالي: {getNextPaymentDate(installment)}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        {installment.customerPhone && (
                          <p className="flex items-center">
                            <span className="text-lg ml-2">📞</span>
                            الهاتف: {installment.customerPhone}
                          </p>
                        )}
                        {installment.productName && (
                          <p className="flex items-center">
                            <span className="text-lg ml-2">📦</span>
                            المنتج: {installment.productName}
                          </p>
                        )}
                        {installment.notes && (
                          <p className="flex items-start">
                            <span className="text-lg ml-2 mt-1">📝</span>
                            <span>ملاحظات: {installment.notes}</span>
                          </p>
                        )}
                      </div>
                    </div>

                    {/* شريط التقدم */}
                    <div className="mb-4">
                      <div className="flex justify-between text-sm text-white text-opacity-60 mb-2">
                        <span>التقدم</span>
                        <span>{progress.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-300 ${
                            installment.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                          }`}
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col space-y-2 mr-4">
                    {installment.status !== 'completed' && (
                      <button
                        onClick={() => {
                          setSelectedInstallment(installment);
                          setShowPaymentModal(true);
                        }}
                        className="bg-green-500 bg-opacity-80 hover:bg-opacity-100 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 text-sm"
                      >
                        <span className="flex items-center">
                          <span className="text-sm ml-1">💳</span>
                          دفع قسط
                        </span>
                      </button>
                    )}
                    <button
                      onClick={() => deleteInstallment(installment.id)}
                      className="bg-red-500 bg-opacity-80 hover:bg-opacity-100 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 text-sm"
                    >
                      <span className="flex items-center">
                        <span className="text-sm ml-1">🗑️</span>
                        حذف
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* نافذة إضافة دفعة */}
      {showPaymentModal && selectedInstallment && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="glass p-8 rounded-3xl max-w-md w-full mx-4">
            <h3 className="text-2xl font-bold text-white mb-6 text-center">
              💳 دفع قسط - {selectedInstallment.customerName}
            </h3>
            
            <div className="space-y-4 mb-6">
              <div className="bg-blue-500 bg-opacity-20 p-4 rounded-2xl border border-blue-500 border-opacity-30">
                <p className="text-blue-300 text-center">
                  💰 مبلغ القسط المطلوب: {selectedInstallment.installmentAmount?.toFixed(2)} د.ع
                </p>
              </div>
              
              <input
                type="number"
                step="0.01"
                placeholder="مبلغ الدفعة"
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
                defaultValue={selectedInstallment.installmentAmount}
                id="paymentAmount"
              />
            </div>
            
            <div className="flex gap-4">
              <button
                onClick={() => {
                  const amount = document.getElementById('paymentAmount').value;
                  if (amount && parseFloat(amount) > 0) {
                    addPayment(selectedInstallment.id, amount);
                  }
                }}
                className="flex-1 bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-2xl font-bold transition-all duration-300"
              >
                ✅ تأكيد الدفع
              </button>
              
              <button
                onClick={() => {
                  setShowPaymentModal(false);
                  setSelectedInstallment(null);
                }}
                className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-2xl font-bold transition-all duration-300"
              >
                ❌ إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* زر إعادة التحميل */}
      <div className="text-center mt-8">
        <button
          onClick={loadInstallments}
          className="glass px-6 py-3 rounded-2xl text-white font-medium transition-all duration-300 transform hover:scale-105"
        >
          <span className="flex items-center justify-center">
            <span className="text-lg ml-2">🔄</span>
            إعادة تحميل الأقساط
          </span>
        </button>
      </div>
    </div>
  );
};

export default Installments;
