import mongoose from 'mongoose';

const saleItemSchema = new mongoose.Schema({
  productId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  name: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  total: {
    type: Number,
    required: true,
    min: 0
  },
  category: String
});

const saleSchema = new mongoose.Schema({
  items: [saleItemSchema],
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  discount: {
    type: Number,
    default: 0,
    min: 0
  },
  total: {
    type: Number,
    required: true,
    min: 0
  },
  customer: {
    type: String,
    trim: true
  },
  paymentMethod: {
    type: String,
    required: true,
    enum: ['نقدي', 'بطاقة', 'تحويل'],
    default: 'نقدي'
  },
  saleDate: {
    type: Date,
    default: Date.now
  },
  cashier: {
    type: String,
    default: 'النظام'
  },
  notes: String,
  status: {
    type: String,
    enum: ['completed', 'cancelled', 'refunded'],
    default: 'completed'
  }
}, {
  timestamps: true
});

// Calculate totals before saving
saleSchema.pre('save', function(next) {
  this.subtotal = this.items.reduce((sum, item) => sum + item.total, 0);
  this.total = this.subtotal - this.discount;
  next();
});

export default mongoose.model('Sale', saleSchema);
