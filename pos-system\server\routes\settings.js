import express from 'express';
import Settings from '../models/Settings.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// Get settings
router.get('/', authenticateToken, async (req, res) => {
  try {
    const settings = await Settings.getSettings();
    
    // Don't send the PIN in the response
    const { pin, ...safeSettings } = settings.toObject();
    
    res.json(safeSettings);
  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({ message: 'خطأ في جلب الإعدادات' });
  }
});

// Update settings
router.put('/', authenticateToken, async (req, res) => {
  try {
    const settings = await Settings.getSettings();
    
    // Update only allowed fields
    const allowedFields = [
      'theme', 'fontSize', 'currency', 'businessName', 
      'businessAddress', 'businessPhone', 'businessEmail',
      'taxRate', 'lowStockAlert', 'autoBackup', 
      'receiptFooter', 'language'
    ];
    
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        settings[field] = req.body[field];
      }
    });
    
    await settings.save();
    
    // Don't send the PIN in the response
    const { pin, ...safeSettings } = settings.toObject();
    
    res.json({ message: 'تم تحديث الإعدادات بنجاح', settings: safeSettings });
  } catch (error) {
    console.error('Update settings error:', error);
    res.status(400).json({ message: 'خطأ في تحديث الإعدادات', error: error.message });
  }
});

// Update PIN
router.put('/pin', authenticateToken, async (req, res) => {
  try {
    const { currentPin, newPin } = req.body;
    
    if (!currentPin || !newPin) {
      return res.status(400).json({ message: 'الرمز الحالي والجديد مطلوبان' });
    }
    
    if (newPin.length < 4) {
      return res.status(400).json({ message: 'الرمز الجديد يجب أن يكون 4 خانات على الأقل' });
    }
    
    const settings = await Settings.getSettings();
    
    if (currentPin !== settings.pin) {
      return res.status(401).json({ message: 'الرمز الحالي غير صحيح' });
    }
    
    settings.pin = newPin;
    await settings.save();
    
    res.json({ message: 'تم تحديث رمز الدخول بنجاح' });
  } catch (error) {
    console.error('Update PIN error:', error);
    res.status(500).json({ message: 'خطأ في تحديث رمز الدخول' });
  }
});

// Reset settings to default
router.post('/reset', authenticateToken, async (req, res) => {
  try {
    const settings = await Settings.getSettings();
    
    // Reset to defaults but keep PIN and business info
    settings.theme = 'light';
    settings.fontSize = '16px';
    settings.taxRate = 0;
    settings.lowStockAlert = 5;
    settings.autoBackup = true;
    settings.language = 'ar';
    
    await settings.save();
    
    const { pin, ...safeSettings } = settings.toObject();
    
    res.json({ message: 'تم إعادة تعيين الإعدادات للافتراضية', settings: safeSettings });
  } catch (error) {
    console.error('Reset settings error:', error);
    res.status(500).json({ message: 'خطأ في إعادة تعيين الإعدادات' });
  }
});

export default router;
