import React, { useState, useEffect } from 'react';

const InstallmentsSimple = () => {
  const [installments, setInstallments] = useState([]);
  const [form, setForm] = useState({
    customerName: '',
    customerPhone: '',
    productName: '',
    totalAmount: '',
    downPayment: '',
    installmentCount: '',
    startDate: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // تحميل البيانات من localStorage عند بدء التطبيق
  useEffect(() => {
    console.log('🔄 تحميل الأقساط من localStorage...');
    const savedInstallments = localStorage.getItem('simple_installments');
    if (savedInstallments) {
      try {
        const parsedInstallments = JSON.parse(savedInstallments);
        setInstallments(parsedInstallments);
        console.log('✅ تم تحميل', parsedInstallments.length, 'قسط من localStorage');
      } catch (error) {
        console.error('❌ خطأ في تحليل البيانات المحفوظة:', error);
      }
    } else {
      console.log('ℹ️ لا توجد أقساط محفوظة');
    }
  }, []);

  const calculateInstallmentDetails = () => {
    const total = parseFloat(form.totalAmount) || 0;
    const down = parseFloat(form.downPayment) || 0;
    const count = parseInt(form.installmentCount) || 1;
    
    const remainingAmount = total - down;
    const monthlyAmount = remainingAmount / count;
    
    return {
      remainingAmount: remainingAmount.toFixed(2),
      monthlyAmount: monthlyAmount.toFixed(2)
    };
  };

  const addInstallment = () => {
    console.log('➕ محاولة إضافة قسط جديد...');
    
    if (!form.customerName || !form.totalAmount || !form.installmentCount || !form.startDate) {
      setError('يرجى ملء جميع الحقول المطلوبة');
      console.log('❌ حقول مفقودة');
      return;
    }

    const { remainingAmount, monthlyAmount } = calculateInstallmentDetails();

    const newInstallment = {
      id: Date.now(),
      customerName: form.customerName,
      customerPhone: form.customerPhone,
      productName: form.productName,
      totalAmount: parseFloat(form.totalAmount),
      downPayment: parseFloat(form.downPayment) || 0,
      remainingAmount: parseFloat(remainingAmount),
      installmentAmount: parseFloat(monthlyAmount),
      installmentCount: parseInt(form.installmentCount),
      paidInstallments: 0,
      startDate: form.startDate,
      status: 'active',
      createdAt: new Date().toISOString()
    };

    console.log('📝 قسط جديد:', newInstallment);

    const updatedInstallments = [newInstallment, ...installments];
    setInstallments(updatedInstallments);
    
    // حفظ في localStorage
    try {
      localStorage.setItem('simple_installments', JSON.stringify(updatedInstallments));
      console.log('💾 تم حفظ الأقساط في localStorage');
    } catch (error) {
      console.error('❌ فشل في حفظ البيانات:', error);
      setError('فشل في حفظ البيانات محلياً');
    }

    setForm({
      customerName: '',
      customerPhone: '',
      productName: '',
      totalAmount: '',
      downPayment: '',
      installmentCount: '',
      startDate: ''
    });
    setError('');
    console.log('✅ تم إضافة القسط بنجاح');
  };

  const deleteInstallment = (id) => {
    console.log('🗑️ محاولة حذف القسط:', id);
    
    if (window.confirm('هل تريد حذف هذا القسط؟')) {
      const updatedInstallments = installments.filter(i => i.id !== id);
      setInstallments(updatedInstallments);
      
      try {
        localStorage.setItem('simple_installments', JSON.stringify(updatedInstallments));
        console.log('✅ تم حذف القسط وحفظ التغييرات');
      } catch (error) {
        console.error('❌ فشل في حفظ التغييرات:', error);
      }
    }
  };

  const payInstallment = (id) => {
    const installment = installments.find(i => i.id === id);
    if (!installment) return;

    const updatedInstallments = installments.map(i => 
      i.id === id 
        ? { 
            ...i, 
            paidInstallments: i.paidInstallments + 1,
            status: i.paidInstallments + 1 >= i.installmentCount ? 'completed' : 'active'
          }
        : i
    );

    setInstallments(updatedInstallments);
    localStorage.setItem('simple_installments', JSON.stringify(updatedInstallments));
    console.log('✅ تم دفع قسط');
  };

  const totalInstallments = installments.length;
  const activeInstallments = installments.filter(i => i.status === 'active').length;
  const completedInstallments = installments.filter(i => i.status === 'completed').length;
  const totalRevenue = installments.reduce((sum, i) => sum + i.totalAmount, 0);

  return (
    <div className="p-6 max-w-7xl mx-auto min-h-screen">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-white mb-2">💰 منصة الأقساط البسيطة</h1>
        <p className="text-white text-opacity-80">نسخة مبسطة للاختبار</p>
      </div>

      {error && (
        <div className="bg-red-500 bg-opacity-20 border border-red-500 text-red-300 p-4 rounded-lg mb-6">
          ❌ {error}
          <button 
            onClick={() => setError('')}
            className="mr-4 text-red-200 hover:text-white"
          >
            إغلاق
          </button>
        </div>
      )}

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white bg-opacity-10 p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">📊</div>
          <h3 className="font-bold text-lg text-white mb-1">إجمالي الأقساط</h3>
          <p className="text-2xl font-bold text-blue-300">{totalInstallments}</p>
        </div>
        
        <div className="bg-white bg-opacity-10 p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">⏰</div>
          <h3 className="font-bold text-lg text-white mb-1">نشطة</h3>
          <p className="text-2xl font-bold text-green-300">{activeInstallments}</p>
        </div>
        
        <div className="bg-white bg-opacity-10 p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">✅</div>
          <h3 className="font-bold text-lg text-white mb-1">مكتملة</h3>
          <p className="text-2xl font-bold text-blue-300">{completedInstallments}</p>
        </div>
        
        <div className="bg-white bg-opacity-10 p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">💰</div>
          <h3 className="font-bold text-lg text-white mb-1">إجمالي الإيرادات</h3>
          <p className="text-xl font-bold text-yellow-300">{totalRevenue.toFixed(2)} د.ع</p>
        </div>
      </div>

      {/* نموذج إضافة قسط جديد */}
      <div className="bg-white bg-opacity-10 p-8 rounded-3xl mb-8">
        <h2 className="text-2xl font-bold text-white mb-6">➕ إضافة قسط جديد</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          <input
            placeholder="اسم العميل *"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={form.customerName}
            onChange={(e) => setForm({ ...form, customerName: e.target.value })}
            required
          />
          
          <input
            placeholder="رقم الهاتف"
            type="tel"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={form.customerPhone}
            onChange={(e) => setForm({ ...form, customerPhone: e.target.value })}
          />
          
          <input
            placeholder="اسم المنتج"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={form.productName}
            onChange={(e) => setForm({ ...form, productName: e.target.value })}
          />
          
          <input
            placeholder="المبلغ الإجمالي *"
            type="number"
            step="0.01"
            min="0"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={form.totalAmount}
            onChange={(e) => setForm({ ...form, totalAmount: e.target.value })}
            required
          />
          
          <input
            placeholder="الدفعة المقدمة"
            type="number"
            step="0.01"
            min="0"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={form.downPayment}
            onChange={(e) => setForm({ ...form, downPayment: e.target.value })}
          />
          
          <input
            placeholder="عدد الأقساط *"
            type="number"
            min="1"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={form.installmentCount}
            onChange={(e) => setForm({ ...form, installmentCount: e.target.value })}
            required
          />
          
          <input
            type="date"
            title="تاريخ بداية الأقساط"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={form.startDate}
            onChange={(e) => setForm({ ...form, startDate: e.target.value })}
            required
          />
        </div>

        {/* معاينة حساب القسط */}
        {form.totalAmount && form.installmentCount && (
          <div className="bg-blue-500 bg-opacity-20 p-4 rounded-2xl mb-6 border border-blue-500 border-opacity-30">
            <h3 className="text-blue-300 font-bold mb-2">📊 معاينة الحساب:</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-blue-200">
              <p>💰 المبلغ المتبقي: {calculateInstallmentDetails().remainingAmount} د.ع</p>
              <p>📅 قسط شهري: {calculateInstallmentDetails().monthlyAmount} د.ع</p>
              <p>🔢 عدد الأقساط: {form.installmentCount} قسط</p>
            </div>
          </div>
        )}

        <button
          onClick={addInstallment}
          disabled={!form.customerName || !form.totalAmount || !form.installmentCount || !form.startDate}
          className="bg-gradient-to-r from-blue-500 to-purple-600 px-8 py-4 rounded-2xl text-white font-bold text-lg hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
        >
          ➕ إضافة قسط
        </button>
      </div>

      {/* قائمة الأقساط */}
      <div className="space-y-6">
        {installments.length === 0 ? (
          <div className="bg-white bg-opacity-10 p-12 rounded-3xl text-center text-white text-opacity-60">
            <div className="text-6xl mb-4">💰</div>
            <p className="text-xl mb-2">لا توجد أقساط مسجلة</p>
            <p className="text-sm opacity-80">ابدأ بإضافة قسط جديد</p>
          </div>
        ) : (
          installments.map((installment) => {
            const progress = (installment.paidInstallments / installment.installmentCount) * 100;
            
            return (
              <div key={installment.id} className="bg-white bg-opacity-10 p-6 rounded-3xl hover:bg-opacity-20 transition-all duration-300">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="font-bold text-xl text-white mb-2">👤 {installment.customerName}</h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-white text-opacity-80 mb-4">
                      <div className="space-y-2">
                        <p>💰 المبلغ الإجمالي: {installment.totalAmount.toFixed(2)} د.ع</p>
                        <p>💵 الدفعة المقدمة: {installment.downPayment.toFixed(2)} د.ع</p>
                        <p>📅 قسط شهري: {installment.installmentAmount.toFixed(2)} د.ع</p>
                      </div>
                      
                      <div className="space-y-2">
                        <p>🔢 الأقساط: {installment.paidInstallments}/{installment.installmentCount}</p>
                        <p>📅 تاريخ البداية: {new Date(installment.startDate).toLocaleDateString('ar-SA')}</p>
                        <p>📊 الحالة: {installment.status === 'completed' ? '✅ مكتمل' : '⏰ نشط'}</p>
                      </div>
                      
                      <div className="space-y-2">
                        {installment.customerPhone && <p>📞 الهاتف: {installment.customerPhone}</p>}
                        {installment.productName && <p>📦 المنتج: {installment.productName}</p>}
                      </div>
                    </div>

                    {/* شريط التقدم */}
                    <div className="mb-4">
                      <div className="flex justify-between text-sm text-white text-opacity-60 mb-2">
                        <span>التقدم</span>
                        <span>{progress.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-300 ${
                            installment.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                          }`}
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col space-y-2 mr-4">
                    {installment.status !== 'completed' && (
                      <button
                        onClick={() => payInstallment(installment.id)}
                        className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300 text-sm"
                      >
                        💳 دفع قسط
                      </button>
                    )}
                    <button
                      onClick={() => deleteInstallment(installment.id)}
                      className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-xl font-medium transition-all duration-300 text-sm"
                    >
                      🗑️ حذف
                    </button>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      <div className="text-center mt-8">
        <p className="text-white text-opacity-60 text-sm">
          البيانات محفوظة في localStorage
        </p>
      </div>
    </div>
  );
};

export default InstallmentsSimple;
