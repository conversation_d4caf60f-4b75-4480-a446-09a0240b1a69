import React, { useState, useEffect } from 'react';

const InstallmentsSimple = () => {
  const [installments, setInstallments] = useState([]);
  const [form, setForm] = useState({
    customerName: '',
    customerPhone: '',
    totalAmount: '',
    interestRate: '',
    monthsCount: ''
  });
  const [editingId, setEditingId] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showInstallmentsModal, setShowInstallmentsModal] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // تحميل البيانات من localStorage عند بدء التطبيق
  useEffect(() => {
    console.log('🔄 تحميل الأقساط من localStorage...');
    const savedInstallments = localStorage.getItem('simple_installments');
    if (savedInstallments) {
      try {
        const parsedInstallments = JSON.parse(savedInstallments);

        // تحديث البيانات القديمة لتشمل قائمة الأقساط
        const updatedInstallments = parsedInstallments.map(installment => {
          if (!installment.installmentsList) {
            // إنشاء قائمة أقساط للبيانات القديمة
            const installmentsList = Array.from({ length: installment.monthsCount || 12 }, (_, index) => ({
              id: index + 1,
              month: index + 1,
              amount: installment.monthlyPayment || 0,
              dueDate: new Date(Date.now() + (index + 1) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
              isPaid: index < (installment.paidInstallments || 0),
              paidDate: index < (installment.paidInstallments || 0) ? new Date().toISOString().split('T')[0] : null
            }));

            return {
              ...installment,
              installmentsList
            };
          }
          return installment;
        });

        setInstallments(updatedInstallments);

        // حفظ البيانات المحدثة
        localStorage.setItem('simple_installments', JSON.stringify(updatedInstallments));

        console.log('✅ تم تحميل', updatedInstallments.length, 'قسط من localStorage');
      } catch (error) {
        console.error('❌ خطأ في تحليل البيانات المحفوظة:', error);
      }
    } else {
      console.log('ℹ️ لا توجد أقساط محفوظة');
    }
  }, []);

  const calculateInstallmentDetails = () => {
    const principal = parseFloat(form.totalAmount) || 0;
    const monthlyInterestRate = parseFloat(form.interestRate) || 0;
    const months = parseInt(form.monthsCount) || 1;

    if (principal <= 0 || months <= 0) {
      return {
        principal: '0.00',
        totalInterest: '0.00',
        totalAmountWithInterest: '0.00',
        monthlyPayment: '0.00'
      };
    }

    // طريقة حساب الفائدة البسيطة الصحيحة
    // الفائدة الشهرية = المبلغ الأساسي × نسبة الفائدة الشهرية ÷ 100
    const monthlyInterest = (principal * monthlyInterestRate) / 100;

    // إجمالي الفوائد = الفائدة الشهرية × عدد الأشهر
    const totalInterest = monthlyInterest * months;

    // المبلغ الإجمالي = المبلغ الأساسي + إجمالي الفوائد
    const totalAmountWithInterest = principal + totalInterest;

    // القسط الشهري = المبلغ الإجمالي ÷ عدد الأشهر
    const monthlyPayment = totalAmountWithInterest / months;

    return {
      principal: principal.toFixed(2),
      monthlyInterest: monthlyInterest.toFixed(2),
      totalInterest: totalInterest.toFixed(2),
      totalAmountWithInterest: totalAmountWithInterest.toFixed(2),
      monthlyPayment: monthlyPayment.toFixed(2)
    };
  };

  const addOrUpdateInstallment = () => {
    console.log('➕ محاولة إضافة/تحديث قسط...');

    if (!form.customerName || !form.totalAmount || !form.interestRate || !form.monthsCount || !form.customerPhone) {
      setError('يرجى ملء جميع الحقول المطلوبة');
      console.log('❌ حقول مفقودة');
      return;
    }

    const { principal, totalInterest, totalAmountWithInterest, monthlyPayment } = calculateInstallmentDetails();

    const installmentData = {
      customerName: form.customerName.trim(),
      customerPhone: form.customerPhone.trim(),
      totalAmount: parseFloat(form.totalAmount),
      interestRate: parseFloat(form.interestRate),
      monthsCount: parseInt(form.monthsCount),
      totalInterest: parseFloat(totalInterest),
      totalAmountWithInterest: parseFloat(totalAmountWithInterest),
      monthlyPayment: parseFloat(monthlyPayment),
      paidInstallments: 0,
      installmentsList: Array.from({ length: parseInt(form.monthsCount) }, (_, index) => ({
        id: index + 1,
        month: index + 1,
        amount: parseFloat(monthlyPayment),
        dueDate: new Date(Date.now() + (index + 1) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        isPaid: false,
        paidDate: null
      })),
      status: 'active',
      createdAt: editingId ? installments.find(i => i.id === editingId)?.createdAt : new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    let updatedInstallments;

    if (editingId) {
      // تحديث قسط موجود
      updatedInstallments = installments.map(installment =>
        installment.id === editingId
          ? { ...installment, ...installmentData }
          : installment
      );
      console.log('✏️ تم تحديث القسط');
    } else {
      // إضافة قسط جديد
      const newInstallment = {
        id: Date.now(),
        ...installmentData
      };
      updatedInstallments = [newInstallment, ...installments];
      console.log('➕ تم إضافة قسط جديد');
    }

    setInstallments(updatedInstallments);

    // حفظ في localStorage
    try {
      localStorage.setItem('simple_installments', JSON.stringify(updatedInstallments));
      console.log('💾 تم حفظ الأقساط في localStorage');
    } catch (error) {
      console.error('❌ فشل في حفظ البيانات:', error);
      setError('فشل في حفظ البيانات محلياً');
    }

    // إعادة تعيين النموذج
    resetForm();
    setError('');
    console.log('✅ تم حفظ القسط بنجاح');
  };

  const resetForm = () => {
    setForm({
      customerName: '',
      customerPhone: '',
      totalAmount: '',
      interestRate: '',
      monthsCount: ''
    });
    setEditingId(null);
    setShowAddForm(false);
  };

  const editInstallment = (installment) => {
    console.log('✏️ تحرير القسط:', installment.id);

    setForm({
      customerName: installment.customerName,
      customerPhone: installment.customerPhone,
      totalAmount: installment.totalAmount.toString(),
      interestRate: installment.interestRate.toString(),
      monthsCount: installment.monthsCount.toString()
    });
    setEditingId(installment.id);
    setShowAddForm(true);
  };

  const deleteInstallment = (id) => {
    console.log('🗑️ محاولة حذف القسط:', id);

    const installment = installments.find(i => i.id === id);
    if (!installment) return;

    if (window.confirm(`هل تريد حذف قسط العميل "${installment.customerName}"؟`)) {
      const updatedInstallments = installments.filter(i => i.id !== id);
      setInstallments(updatedInstallments);

      try {
        localStorage.setItem('simple_installments', JSON.stringify(updatedInstallments));
        console.log('✅ تم حذف القسط وحفظ التغييرات');
      } catch (error) {
        console.error('❌ فشل في حفظ التغييرات:', error);
      }

      // إذا كان القسط المحذوف هو المحرر حالياً، إعادة تعيين النموذج
      if (editingId === id) {
        resetForm();
      }
    }
  };

  const payInstallment = (customerId, installmentId) => {
    const updatedInstallments = installments.map(customer => {
      if (customer.id === customerId) {
        const updatedInstallmentsList = customer.installmentsList.map(inst =>
          inst.id === installmentId
            ? { ...inst, isPaid: true, paidDate: new Date().toISOString().split('T')[0] }
            : inst
        );

        const paidCount = updatedInstallmentsList.filter(inst => inst.isPaid).length;

        return {
          ...customer,
          installmentsList: updatedInstallmentsList,
          paidInstallments: paidCount,
          status: paidCount >= customer.monthsCount ? 'completed' : 'active'
        };
      }
      return customer;
    });

    setInstallments(updatedInstallments);
    localStorage.setItem('simple_installments', JSON.stringify(updatedInstallments));

    // تحديث العميل المحدد في النافذة المنبثقة
    if (selectedCustomer && selectedCustomer.id === customerId) {
      const updatedCustomer = updatedInstallments.find(c => c.id === customerId);
      setSelectedCustomer(updatedCustomer);
    }

    console.log('✅ تم دفع قسط');
  };

  const cancelPayment = (customerId, installmentId) => {
    const updatedInstallments = installments.map(customer => {
      if (customer.id === customerId) {
        const updatedInstallmentsList = customer.installmentsList.map(inst =>
          inst.id === installmentId
            ? { ...inst, isPaid: false, paidDate: null }
            : inst
        );

        const paidCount = updatedInstallmentsList.filter(inst => inst.isPaid).length;

        return {
          ...customer,
          installmentsList: updatedInstallmentsList,
          paidInstallments: paidCount,
          status: paidCount >= customer.monthsCount ? 'completed' : 'active'
        };
      }
      return customer;
    });

    setInstallments(updatedInstallments);
    localStorage.setItem('simple_installments', JSON.stringify(updatedInstallments));

    // تحديث العميل المحدد في النافذة المنبثقة
    if (selectedCustomer && selectedCustomer.id === customerId) {
      const updatedCustomer = updatedInstallments.find(c => c.id === customerId);
      setSelectedCustomer(updatedCustomer);
    }

    console.log('✅ تم إلغاء دفع القسط');
  };

  const openInstallmentsModal = (customer) => {
    setSelectedCustomer(customer);
    setShowInstallmentsModal(true);
  };

  const closeInstallmentsModal = () => {
    setShowInstallmentsModal(false);
    setSelectedCustomer(null);
  };

  const totalInstallments = installments.length;
  const activeInstallments = installments.filter(i => i.status === 'active').length;
  const completedInstallments = installments.filter(i => i.status === 'completed').length;
  const totalRevenue = installments.reduce((sum, i) => sum + i.totalAmountWithInterest, 0);
  const totalInterestEarned = installments.reduce((sum, i) => sum + i.totalInterest, 0);

  return (
    <div className="p-6 max-w-7xl mx-auto min-h-screen">
      <div className="text-center mb-8 animate-fadeInUp">
        <h1 className="text-4xl font-bold text-gradient mb-2">💰 منصة الأقساط</h1>
        <p className="text-white text-opacity-80">إدارة أقساط العملاء بسهولة ووضوح</p>
      </div>

      {error && (
        <div className="bg-red-500 bg-opacity-20 border border-red-500 text-red-300 p-4 rounded-lg mb-6">
          ❌ {error}
          <button 
            onClick={() => setError('')}
            className="mr-4 text-red-200 hover:text-white"
          >
            إغلاق
          </button>
        </div>
      )}

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <div className="glass p-6 rounded-3xl text-center animate-pulse-custom">
          <div className="text-4xl mb-2">📊</div>
          <h3 className="font-bold text-lg text-white mb-1">إجمالي العملاء</h3>
          <p className="text-2xl font-bold text-blue-300">{totalInstallments}</p>
        </div>

        <div className="glass p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">⏰</div>
          <h3 className="font-bold text-lg text-white mb-1">أقساط نشطة</h3>
          <p className="text-2xl font-bold text-green-300">{activeInstallments}</p>
        </div>

        <div className="glass p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">✅</div>
          <h3 className="font-bold text-lg text-white mb-1">أقساط مكتملة</h3>
          <p className="text-2xl font-bold text-blue-300">{completedInstallments}</p>
        </div>

        <div className="glass p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">💰</div>
          <h3 className="font-bold text-lg text-white mb-1">إجمالي المبالغ</h3>
          <p className="text-xl font-bold text-yellow-300">{totalRevenue.toFixed(2)} د.ع</p>
        </div>

        <div className="glass p-6 rounded-3xl text-center">
          <div className="text-4xl mb-2">📈</div>
          <h3 className="font-bold text-lg text-white mb-1">إجمالي الفوائد</h3>
          <p className="text-xl font-bold text-green-300">{totalInterestEarned.toFixed(2)} د.ع</p>
        </div>
      </div>

      {/* أمثلة حسابية سريعة */}
      <div className="glass p-6 rounded-3xl mb-8">
        <h3 className="text-xl font-bold text-white mb-4 text-center">🧮 أمثلة حسابية سريعة</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="bg-blue-500 bg-opacity-20 p-4 rounded-2xl">
            <h4 className="text-blue-300 font-bold mb-2">مثال 1: قسط بسيط</h4>
            <p className="text-white text-opacity-80">💰 المبلغ: 1000 د.ع</p>
            <p className="text-white text-opacity-80">📈 الفائدة: 5% شهرياً</p>
            <p className="text-white text-opacity-80">📅 المدة: 10 أشهر</p>
            <hr className="my-2 border-blue-300 border-opacity-30" />
            <p className="text-blue-300">🔢 الفائدة الشهرية: 50 د.ع</p>
            <p className="text-green-300">📊 إجمالي الفوائد: 500 د.ع</p>
            <p className="text-yellow-300">💳 القسط الشهري: 150 د.ع</p>
          </div>

          <div className="bg-green-500 bg-opacity-20 p-4 rounded-2xl">
            <h4 className="text-green-300 font-bold mb-2">مثال 2: قسط متوسط</h4>
            <p className="text-white text-opacity-80">💰 المبلغ: 5000 د.ع</p>
            <p className="text-white text-opacity-80">📈 الفائدة: 3% شهرياً</p>
            <p className="text-white text-opacity-80">📅 المدة: 12 شهر</p>
            <hr className="my-2 border-green-300 border-opacity-30" />
            <p className="text-blue-300">🔢 الفائدة الشهرية: 150 د.ع</p>
            <p className="text-green-300">📊 إجمالي الفوائد: 1800 د.ع</p>
            <p className="text-yellow-300">💳 القسط الشهري: 566.67 د.ع</p>
          </div>

          <div className="bg-purple-500 bg-opacity-20 p-4 rounded-2xl">
            <h4 className="text-purple-300 font-bold mb-2">مثال 3: قسط كبير</h4>
            <p className="text-white text-opacity-80">💰 المبلغ: 10000 د.ع</p>
            <p className="text-white text-opacity-80">📈 الفائدة: 2% شهرياً</p>
            <p className="text-white text-opacity-80">📅 المدة: 24 شهر</p>
            <hr className="my-2 border-purple-300 border-opacity-30" />
            <p className="text-blue-300">🔢 الفائدة الشهرية: 200 د.ع</p>
            <p className="text-green-300">📊 إجمالي الفوائد: 4800 د.ع</p>
            <p className="text-yellow-300">💳 القسط الشهري: 616.67 د.ع</p>
          </div>
        </div>
      </div>

      {/* زر إضافة عميل جديد */}
      <div className="text-center mb-8">
        <button
          onClick={() => setShowAddForm(!showAddForm)}
          className="btn-gradient px-8 py-4 rounded-2xl text-white font-bold text-lg transform hover:scale-105 transition-all duration-300 shadow-glow"
        >
          <span className="flex items-center justify-center">
            <span className="text-xl ml-2">{showAddForm ? '❌' : '➕'}</span>
            {showAddForm ? 'إلغاء' : 'إضافة عميل جديد'}
          </span>
        </button>
      </div>

      {/* نموذج إضافة/تحرير عميل */}
      {showAddForm && (
        <div className="glass p-8 rounded-3xl shadow-glow mb-8 animate-slideInRight">
          <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
            <span className="text-3xl ml-3">{editingId ? '✏️' : '➕'}</span>
            {editingId ? 'تحرير بيانات العميل' : 'إضافة عميل جديد'}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className="block text-white font-medium mb-2">اسم العميل *</label>
              <input
                placeholder="أدخل اسم العميل"
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
                value={form.customerName}
                onChange={(e) => setForm({ ...form, customerName: e.target.value })}
                required
              />
            </div>

            <div>
              <label className="block text-white font-medium mb-2">رقم الهاتف *</label>
              <input
                placeholder="أدخل رقم الهاتف"
                type="tel"
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
                value={form.customerPhone}
                onChange={(e) => setForm({ ...form, customerPhone: e.target.value })}
                required
              />
            </div>

            <div>
              <label className="block text-white font-medium mb-2">مبلغ القسط الكامل *</label>
              <input
                placeholder="أدخل المبلغ الأساسي"
                type="number"
                step="0.01"
                min="0"
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
                value={form.totalAmount}
                onChange={(e) => setForm({ ...form, totalAmount: e.target.value })}
                required
              />
            </div>

            <div>
              <label className="block text-white font-medium mb-2">الفائدة الشهرية (%) *</label>
              <input
                placeholder="أدخل نسبة الفائدة الشهرية"
                type="number"
                step="0.1"
                min="0"
                max="100"
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
                value={form.interestRate}
                onChange={(e) => setForm({ ...form, interestRate: e.target.value })}
                required
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-white font-medium mb-2">عدد الأشهر *</label>
              <input
                placeholder="أدخل عدد الأشهر"
                type="number"
                min="1"
                max="120"
                className="input-glow border-0 p-4 w-full rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70 transition-all duration-300 focus:outline-none backdrop-blur-sm"
                value={form.monthsCount}
                onChange={(e) => setForm({ ...form, monthsCount: e.target.value })}
                required
              />
            </div>
          </div>

          {/* معاينة حساب القسط */}
          {form.totalAmount && form.interestRate && form.monthsCount && (
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 bg-opacity-20 p-6 rounded-2xl mb-6 border border-blue-500 border-opacity-30">
              <h3 className="text-blue-300 font-bold mb-4 text-center">📊 معاينة الحساب التفصيلية</h3>

              {/* الحسابات الأساسية */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 text-center mb-4">
                <div className="bg-white bg-opacity-10 p-4 rounded-xl">
                  <p className="text-white text-opacity-80 text-sm">💰 المبلغ الأساسي</p>
                  <p className="text-blue-300 font-bold text-lg">{calculateInstallmentDetails().principal} د.ع</p>
                </div>
                <div className="bg-white bg-opacity-10 p-4 rounded-xl">
                  <p className="text-white text-opacity-80 text-sm">📈 الفائدة الشهرية</p>
                  <p className="text-orange-300 font-bold text-lg">{calculateInstallmentDetails().monthlyInterest} د.ع</p>
                </div>
                <div className="bg-white bg-opacity-10 p-4 rounded-xl">
                  <p className="text-white text-opacity-80 text-sm">📊 إجمالي الفوائد</p>
                  <p className="text-green-300 font-bold text-lg">{calculateInstallmentDetails().totalInterest} د.ع</p>
                </div>
                <div className="bg-white bg-opacity-10 p-4 rounded-xl">
                  <p className="text-white text-opacity-80 text-sm">💸 المبلغ الإجمالي</p>
                  <p className="text-yellow-300 font-bold text-lg">{calculateInstallmentDetails().totalAmountWithInterest} د.ع</p>
                </div>
                <div className="bg-white bg-opacity-10 p-4 rounded-xl">
                  <p className="text-white text-opacity-80 text-sm">💳 القسط الشهري</p>
                  <p className="text-purple-300 font-bold text-lg">{calculateInstallmentDetails().monthlyPayment} د.ع</p>
                </div>
              </div>

              {/* شرح الحساب */}
              <div className="bg-white bg-opacity-5 p-4 rounded-xl text-center">
                <p className="text-white text-opacity-70 text-sm">
                  📝 <strong>طريقة الحساب:</strong>
                  الفائدة الشهرية = {form.totalAmount} × {form.interestRate}% = {calculateInstallmentDetails().monthlyInterest} د.ع |
                  إجمالي الفوائد = {calculateInstallmentDetails().monthlyInterest} × {form.monthsCount} شهر = {calculateInstallmentDetails().totalInterest} د.ع
                </p>
              </div>
            </div>
          )}

          <div className="flex gap-4">
            <button
              onClick={addOrUpdateInstallment}
              disabled={!form.customerName || !form.totalAmount || !form.interestRate || !form.monthsCount || !form.customerPhone}
              className="flex-1 btn-gradient px-8 py-4 rounded-2xl text-white font-bold text-lg transform hover:scale-105 transition-all duration-300 shadow-glow disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              <span className="flex items-center justify-center">
                <span className="text-xl ml-2">{editingId ? '✅' : '➕'}</span>
                {editingId ? 'حفظ التعديلات' : 'إضافة العميل'}
              </span>
            </button>

            <button
              onClick={resetForm}
              className="px-6 py-4 rounded-2xl text-white font-bold text-lg bg-gray-500 bg-opacity-80 hover:bg-opacity-100 transform hover:scale-105 transition-all duration-300"
            >
              <span className="flex items-center justify-center">
                <span className="text-xl ml-2">❌</span>
                إلغاء
              </span>
            </button>
          </div>
        </div>
      )}

      {/* قائمة العملاء */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
          <span className="text-3xl ml-3">👥</span>
          قائمة العملاء ({installments.length})
        </h2>

        {installments.length === 0 ? (
          <div className="glass p-12 rounded-3xl text-center text-white text-opacity-60">
            <div className="text-6xl mb-4">👥</div>
            <p className="text-xl mb-2">لا يوجد عملاء مسجلين</p>
            <p className="text-sm opacity-80">اضغط على "إضافة عميل جديد" لبدء إضافة العملاء</p>
          </div>
        ) : (
          installments.map((installment, index) => {
            const progress = (installment.paidInstallments / installment.monthsCount) * 100;
            const isCompleted = installment.status === 'completed';

            return (
              <div
                key={installment.id}
                className="glass p-6 rounded-3xl shadow-glow card-hover animate-fadeInUp"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex justify-between items-start mb-6">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-bold text-2xl text-white flex items-center">
                        <span className="text-3xl ml-3">👤</span>
                        {installment.customerName}
                      </h3>

                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className={`px-4 py-2 text-sm rounded-full font-medium ${
                          isCompleted ? 'bg-green-500 bg-opacity-30 text-green-300' :
                          'bg-blue-500 bg-opacity-30 text-blue-300'
                        }`}>
                          {isCompleted ? '✅ مكتمل' : '⏰ نشط'}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 text-white text-opacity-90 mb-6">
                      <div className="bg-white bg-opacity-10 p-4 rounded-2xl">
                        <h4 className="font-bold text-blue-300 mb-3">💰 المعلومات المالية</h4>
                        <div className="space-y-2 text-sm">
                          <p>💵 المبلغ الأساسي: <span className="font-bold text-blue-300">{installment.totalAmount.toFixed(2)} د.ع</span></p>
                          <p>📈 نسبة الفائدة: <span className="font-bold text-green-300">{installment.interestRate}%</span></p>
                          <p>💸 إجمالي الفوائد: <span className="font-bold text-green-300">{installment.totalInterest.toFixed(2)} د.ع</span></p>
                          <p>💰 المبلغ الإجمالي: <span className="font-bold text-yellow-300">{installment.totalAmountWithInterest.toFixed(2)} د.ع</span></p>
                        </div>
                      </div>

                      <div className="bg-white bg-opacity-10 p-4 rounded-2xl">
                        <h4 className="font-bold text-purple-300 mb-3">📅 معلومات الأقساط</h4>
                        <div className="space-y-2 text-sm">
                          <p>🔢 عدد الأشهر: <span className="font-bold text-purple-300">{installment.monthsCount} شهر</span></p>
                          <p>💳 القسط الشهري: <span className="font-bold text-purple-300">{installment.monthlyPayment.toFixed(2)} د.ع</span></p>
                          <p>✅ الأقساط المدفوعة: <span className="font-bold text-green-300">{installment.paidInstallments}</span></p>
                          <p>⏳ الأقساط المتبقية: <span className="font-bold text-orange-300">{installment.monthsCount - installment.paidInstallments}</span></p>
                        </div>
                      </div>

                      <div className="bg-white bg-opacity-10 p-4 rounded-2xl">
                        <h4 className="font-bold text-cyan-300 mb-3">📞 معلومات الاتصال</h4>
                        <div className="space-y-2 text-sm">
                          <p>📱 الهاتف: <span className="font-bold text-cyan-300">{installment.customerPhone}</span></p>
                          <p>📅 تاريخ الإنشاء: <span className="font-bold text-gray-300">{new Date(installment.createdAt).toLocaleDateString('ar-SA')}</span></p>
                          {installment.updatedAt && installment.updatedAt !== installment.createdAt && (
                            <p>🔄 آخر تحديث: <span className="font-bold text-gray-300">{new Date(installment.updatedAt).toLocaleDateString('ar-SA')}</span></p>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* شريط التقدم */}
                    <div className="mb-4">
                      <div className="flex justify-between text-sm text-white text-opacity-80 mb-2">
                        <span className="font-medium">تقدم السداد</span>
                        <span className="font-bold">{progress.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-white bg-opacity-20 rounded-full h-3">
                        <div
                          className={`h-3 rounded-full transition-all duration-500 ${
                            isCompleted ? 'bg-gradient-to-r from-green-400 to-green-600' :
                            'bg-gradient-to-r from-blue-400 to-purple-600'
                          }`}
                          style={{ width: `${progress}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col space-y-3 mr-6">
                    <button
                      onClick={() => openInstallmentsModal(installment)}
                      className="bg-purple-500 bg-opacity-80 hover:bg-opacity-100 text-white px-5 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 text-sm shadow-lg"
                    >
                      <span className="flex items-center">
                        <span className="text-lg ml-2">📋</span>
                        جدول الأقساط
                      </span>
                    </button>

                    <button
                      onClick={() => editInstallment(installment)}
                      className="bg-blue-500 bg-opacity-80 hover:bg-opacity-100 text-white px-5 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 text-sm shadow-lg"
                    >
                      <span className="flex items-center">
                        <span className="text-lg ml-2">✏️</span>
                        تعديل
                      </span>
                    </button>

                    <button
                      onClick={() => deleteInstallment(installment.id)}
                      className="bg-red-500 bg-opacity-80 hover:bg-opacity-100 text-white px-5 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 text-sm shadow-lg"
                    >
                      <span className="flex items-center">
                        <span className="text-lg ml-2">🗑️</span>
                        حذف
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* نافذة جدول الأقساط المنبثقة */}
      {showInstallmentsModal && selectedCustomer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="glass p-8 rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-2xl font-bold text-white flex items-center">
                <span className="text-3xl ml-3">📋</span>
                جدول أقساط - {selectedCustomer.customerName}
              </h3>
              <button
                onClick={closeInstallmentsModal}
                className="bg-red-500 bg-opacity-80 hover:bg-opacity-100 text-white p-2 rounded-xl transition-all duration-300"
              >
                <span className="text-xl">❌</span>
              </button>
            </div>

            {/* معلومات العميل السريعة */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-blue-500 bg-opacity-20 p-4 rounded-2xl text-center">
                <p className="text-blue-300 font-bold">💰 المبلغ الإجمالي</p>
                <p className="text-white text-xl">{selectedCustomer.totalAmountWithInterest.toFixed(2)} د.ع</p>
              </div>
              <div className="bg-green-500 bg-opacity-20 p-4 rounded-2xl text-center">
                <p className="text-green-300 font-bold">💳 القسط الشهري</p>
                <p className="text-white text-xl">{selectedCustomer.monthlyPayment.toFixed(2)} د.ع</p>
              </div>
              <div className="bg-purple-500 bg-opacity-20 p-4 rounded-2xl text-center">
                <p className="text-purple-300 font-bold">📊 التقدم</p>
                <p className="text-white text-xl">{selectedCustomer.paidInstallments}/{selectedCustomer.monthsCount}</p>
              </div>
            </div>

            {/* جدول الأقساط */}
            <div className="overflow-x-auto">
              <table className="w-full text-white">
                <thead>
                  <tr className="bg-white bg-opacity-10">
                    <th className="p-4 text-right rounded-tr-2xl">الشهر</th>
                    <th className="p-4 text-right">المبلغ</th>
                    <th className="p-4 text-right">تاريخ الاستحقاق</th>
                    <th className="p-4 text-right">الحالة</th>
                    <th className="p-4 text-right">تاريخ الدفع</th>
                    <th className="p-4 text-right rounded-tl-2xl">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {selectedCustomer.installmentsList?.map((installment, index) => (
                    <tr
                      key={installment.id}
                      className={`border-b border-white border-opacity-10 hover:bg-white hover:bg-opacity-5 transition-all duration-200 ${
                        installment.isPaid ? 'bg-green-500 bg-opacity-10' : ''
                      }`}
                    >
                      <td className="p-4 font-bold">
                        <span className="flex items-center">
                          <span className="text-lg ml-2">📅</span>
                          الشهر {installment.month}
                        </span>
                      </td>
                      <td className="p-4">
                        <span className="text-yellow-300 font-bold">{installment.amount.toFixed(2)} د.ع</span>
                      </td>
                      <td className="p-4">
                        <span className="text-blue-300">{new Date(installment.dueDate).toLocaleDateString('ar-SA')}</span>
                      </td>
                      <td className="p-4">
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                          installment.isPaid
                            ? 'bg-green-500 bg-opacity-30 text-green-300'
                            : 'bg-orange-500 bg-opacity-30 text-orange-300'
                        }`}>
                          {installment.isPaid ? '✅ مدفوع' : '⏳ معلق'}
                        </span>
                      </td>
                      <td className="p-4">
                        {installment.isPaid && installment.paidDate ? (
                          <span className="text-green-300">{new Date(installment.paidDate).toLocaleDateString('ar-SA')}</span>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="p-4">
                        <div className="flex gap-2">
                          {!installment.isPaid ? (
                            <button
                              onClick={() => payInstallment(selectedCustomer.id, installment.id)}
                              className="bg-green-500 bg-opacity-80 hover:bg-opacity-100 text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 transform hover:scale-105"
                            >
                              <span className="flex items-center">
                                <span className="text-sm ml-1">💳</span>
                                دفع
                              </span>
                            </button>
                          ) : (
                            <button
                              onClick={() => cancelPayment(selectedCustomer.id, installment.id)}
                              className="bg-red-500 bg-opacity-80 hover:bg-opacity-100 text-white px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 transform hover:scale-105"
                            >
                              <span className="flex items-center">
                                <span className="text-sm ml-1">❌</span>
                                إلغاء
                              </span>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* ملخص سريع */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white bg-opacity-10 p-4 rounded-2xl">
                <h4 className="text-green-300 font-bold mb-2">✅ الأقساط المدفوعة</h4>
                <p className="text-white text-lg">{selectedCustomer.paidInstallments} من {selectedCustomer.monthsCount}</p>
                <p className="text-green-300 text-sm">
                  المبلغ المدفوع: {(selectedCustomer.paidInstallments * selectedCustomer.monthlyPayment).toFixed(2)} د.ع
                </p>
              </div>
              <div className="bg-white bg-opacity-10 p-4 rounded-2xl">
                <h4 className="text-orange-300 font-bold mb-2">⏳ الأقساط المتبقية</h4>
                <p className="text-white text-lg">{selectedCustomer.monthsCount - selectedCustomer.paidInstallments} قسط</p>
                <p className="text-orange-300 text-sm">
                  المبلغ المتبقي: {((selectedCustomer.monthsCount - selectedCustomer.paidInstallments) * selectedCustomer.monthlyPayment).toFixed(2)} د.ع
                </p>
              </div>
            </div>

            {/* زر إغلاق */}
            <div className="text-center mt-6">
              <button
                onClick={closeInstallmentsModal}
                className="bg-gray-500 bg-opacity-80 hover:bg-opacity-100 text-white px-8 py-3 rounded-2xl font-bold transition-all duration-300 transform hover:scale-105"
              >
                <span className="flex items-center justify-center">
                  <span className="text-lg ml-2">✅</span>
                  إغلاق
                </span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* معلومات إضافية */}
      <div className="text-center mt-8">
        <div className="glass p-6 rounded-3xl">
          <h3 className="text-xl font-bold text-white mb-4">📊 معلومات النظام</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-white text-opacity-80">
            <div>
              <p className="font-medium">💾 طريقة الحفظ</p>
              <p className="text-blue-300">localStorage (محلي)</p>
            </div>
            <div>
              <p className="font-medium">🔄 آخر تحديث</p>
              <p className="text-green-300">{new Date().toLocaleString('ar-SA')}</p>
            </div>
            <div>
              <p className="font-medium">📱 حالة النظام</p>
              <p className="text-yellow-300">يعمل بشكل مثالي</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InstallmentsSimple;
