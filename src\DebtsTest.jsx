import React, { useState, useEffect } from 'react';

const DebtsTest = () => {
  const [debts, setDebts] = useState([]);
  const [form, setForm] = useState({ customerName: '', amount: '', date: '' });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // اختبار بسيط بدون Firebase أولاً
  const addDebt = () => {
    if (!form.customerName || !form.amount || !form.date) {
      setError('يرجى ملء جميع الحقول');
      return;
    }

    const newDebt = {
      id: Date.now(),
      customerName: form.customerName,
      amount: parseFloat(form.amount),
      date: form.date,
      createdAt: new Date()
    };

    setDebts(prevDebts => [newDebt, ...prevDebts]);
    setForm({ customerName: '', amount: '', date: '' });
    setError('');

    // حفظ في localStorage
    const updatedDebts = [newDebt, ...debts];
    localStorage.setItem('test_debts', JSON.stringify(updatedDebts));
  };

  const deleteDebt = (id) => {
    if (window.confirm('هل تريد حذف هذا الدين؟')) {
      const updatedDebts = debts.filter(d => d.id !== id);
      setDebts(updatedDebts);
      localStorage.setItem('test_debts', JSON.stringify(updatedDebts));
    }
  };

  // تحميل البيانات من localStorage عند بدء التطبيق
  useEffect(() => {
    const savedDebts = localStorage.getItem('test_debts');
    if (savedDebts) {
      setDebts(JSON.parse(savedDebts));
    }
  }, []);

  const totalDebts = debts.reduce((sum, debt) => sum + debt.amount, 0);

  return (
    <div className="p-6 max-w-5xl mx-auto min-h-screen">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-white mb-2">💳 اختبار الديون</h1>
        <p className="text-white text-opacity-80">نسخة تجريبية للاختبار</p>
      </div>

      {error && (
        <div className="bg-red-500 bg-opacity-20 border border-red-500 text-red-300 p-4 rounded-lg mb-6">
          ❌ {error}
        </div>
      )}

      <div className="bg-white bg-opacity-10 p-8 rounded-3xl mb-8 text-center">
        <h2 className="font-bold text-2xl text-white mb-2">إجمالي الديون</h2>
        <p className="text-4xl font-bold text-yellow-300">{totalDebts.toFixed(2)} دينار</p>
        <p className="text-white text-opacity-70 mt-2">{debts.length} دين</p>
      </div>

      <div className="bg-white bg-opacity-10 p-8 rounded-3xl mb-8">
        <h2 className="text-2xl font-bold text-white mb-6">إضافة دين جديد</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <input
            placeholder="اسم العميل"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70"
            value={form.customerName}
            onChange={(e) => setForm({ ...form, customerName: e.target.value })}
          />
          <input
            placeholder="المبلغ"
            type="number"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white placeholder-white placeholder-opacity-70"
            value={form.amount}
            onChange={(e) => setForm({ ...form, amount: e.target.value })}
          />
          <input
            type="date"
            className="p-4 rounded-2xl bg-white bg-opacity-20 text-white"
            value={form.date}
            onChange={(e) => setForm({ ...form, date: e.target.value })}
          />
        </div>

        <button
          onClick={addDebt}
          className="bg-gradient-to-r from-blue-500 to-purple-600 px-8 py-4 rounded-2xl text-white font-bold text-lg hover:scale-105 transition-all duration-300"
        >
          ➕ إضافة دين
        </button>
      </div>

      <div className="space-y-6">
        {debts.length === 0 ? (
          <div className="bg-white bg-opacity-10 p-12 rounded-3xl text-center text-white text-opacity-60">
            <div className="text-6xl mb-4">💳</div>
            <p className="text-xl mb-2">لا توجد ديون مسجلة</p>
          </div>
        ) : (
          debts.map((debt) => (
            <div key={debt.id} className="bg-white bg-opacity-10 p-6 rounded-3xl flex justify-between items-center">
              <div className="text-white">
                <h3 className="font-bold text-xl mb-2">👤 {debt.customerName}</h3>
                <p className="text-lg">💰 المبلغ: {debt.amount} دينار</p>
                <p>📅 التاريخ: {debt.date}</p>
              </div>
              <button
                onClick={() => deleteDebt(debt.id)}
                className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-2xl font-medium transition-all duration-300"
              >
                🗑️ حذف
              </button>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default DebtsTest;
