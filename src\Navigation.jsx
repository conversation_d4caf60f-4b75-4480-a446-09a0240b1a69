import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import ThemeToggle from './components/ThemeToggle';

const Navigation = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // لا تظهر القائمة في صفحة تسجيل الدخول
  if (location.pathname === '/login') {
    return null;
  }

  const handleLogout = () => {
    if (window.confirm('هل تريد تسجيل الخروج؟')) {
      navigate('/login');
    }
  };

  const navItems = [
    { path: '/sales', label: '📦 المبيعات', icon: '📦' },
    { path: '/inventory', label: '📋 المخزون', icon: '📋' },
    { path: '/reports', label: '📊 التقارير', icon: '📊' },
    { path: '/debts', label: '💳 الديون', icon: '💳' },
    { path: '/settings', label: '⚙️ الإعدادات', icon: '⚙️' },
  ];

  return (
    <nav className="glass text-white p-4 shadow-glow sticky top-0 z-50 animate-fadeInUp">
      <div className="max-w-6xl mx-auto flex justify-between items-center">
        <div className="flex space-x-4 space-x-reverse">
          {navItems.map((item, index) => (
            <Link
              key={item.path}
              to={item.path}
              className={`px-4 py-2 rounded-xl transition-all duration-300 transform hover:scale-105 animate-slideInRight ${
                location.pathname === item.path
                  ? 'bg-white bg-opacity-20 text-white shadow-glow backdrop-blur-sm'
                  : 'hover:bg-white hover:bg-opacity-10 hover:shadow-lg'
              }`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <span className="text-lg">{item.icon}</span>
              <span className="mr-2 font-medium">{item.label.replace(item.icon + ' ', '')}</span>
            </Link>
          ))}
        </div>

        <div className="flex items-center space-x-4 space-x-reverse animate-slideInRight" style={{ animationDelay: '0.5s' }}>
          <span className="text-gradient text-lg font-bold">🏪 نظام نقاط البيع</span>

          {/* زر تبديل الوضع الداكن */}
          <ThemeToggle />

          <button
            onClick={handleLogout}
            className="btn-gradient px-4 py-2 rounded-xl text-sm font-medium transform hover:scale-105 transition-all duration-300"
          >
            🚪 خروج
          </button>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
