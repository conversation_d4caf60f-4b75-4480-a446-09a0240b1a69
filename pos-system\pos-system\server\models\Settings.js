import mongoose from 'mongoose';

const settingsSchema = new mongoose.Schema({
  pin: {
    type: String,
    required: true,
    minlength: 4
  },
  theme: {
    type: String,
    enum: ['light', 'dark'],
    default: 'light'
  },
  fontSize: {
    type: String,
    enum: ['14px', '16px', '18px'],
    default: '16px'
  },
  currency: {
    type: String,
    default: 'د.ع'
  },
  businessName: {
    type: String,
    default: 'نظام نقاط البيع'
  },
  businessAddress: String,
  businessPhone: String,
  businessEmail: String,
  taxRate: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  lowStockAlert: {
    type: Number,
    default: 5,
    min: 0
  },
  autoBackup: {
    type: Boolean,
    default: true
  },
  receiptFooter: String,
  language: {
    type: String,
    enum: ['ar', 'en'],
    default: 'ar'
  }
}, {
  timestamps: true
});

// Ensure only one settings document exists
settingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({
      pin: process.env.DEFAULT_PIN || 'alihatem123'
    });
  }
  return settings;
};

export default mongoose.model('Settings', settingsSchema);
